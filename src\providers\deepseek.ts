import axios, { AxiosInstance, AxiosError } from 'axios';
import { LLMMessage, ToolCall, ToolDefinition, ProviderError } from '../types/index.js';
import pRetry from 'p-retry';
import pTimeout from 'p-timeout';

export interface DeepseekConfig {
  apiKey: string;
  model: string;
  baseUrl?: string;
  maxTokens?: number;
  temperature?: number;
  timeout?: number;
}

export interface DeepseekResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string | null;
      tool_calls?: Array<{
        id: string;
        type: 'function';
        function: {
          name: string;
          arguments: string;
        };
      }>;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export class DeepseekProvider {
  private client: AxiosInstance;
  private config: DeepseekConfig;

  constructor(config: DeepseekConfig) {
    this.config = {
      baseUrl: 'https://api.deepseek.com/v1',
      maxTokens: 4096,
      temperature: 0.7,
      timeout: 30000,
      ...config
    };

    this.client = axios.create({
      baseURL: this.config.baseUrl,
      timeout: this.config.timeout,
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Arien-AI-CLI/1.0.0'
      }
    });

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error: AxiosError) => {
        if (error.response?.status === 429) {
          throw new ProviderError('Rate limit exceeded. Please try again later.', {
            status: 429,
            retryAfter: error.response.headers['retry-after']
          });
        }
        
        if (error.response?.status === 401) {
          throw new ProviderError('Invalid API key. Please check your configuration.', {
            status: 401
          });
        }

        if (error.response?.status === 403) {
          throw new ProviderError('Access forbidden. Please check your API key permissions.', {
            status: 403
          });
        }

        if (error.code === 'ECONNABORTED') {
          throw new ProviderError('Request timeout. Please try again.', {
            timeout: this.config.timeout
          });
        }

        throw new ProviderError(
          `API request failed: ${error.message}`,
          { 
            status: error.response?.status,
            data: error.response?.data 
          }
        );
      }
    );
  }

  public async generateResponse(
    messages: LLMMessage[],
    tools?: ToolDefinition[],
    options: {
      maxTokens?: number;
      temperature?: number;
      stream?: boolean;
    } = {}
  ): Promise<LLMMessage> {
    const requestConfig = {
      model: this.config.model,
      messages: this.formatMessages(messages),
      max_tokens: options.maxTokens || this.config.maxTokens,
      temperature: options.temperature || this.config.temperature,
      stream: options.stream || false,
      tools: tools ? this.formatTools(tools) : undefined,
      tool_choice: tools && tools.length > 0 ? 'auto' : undefined
    };

    try {
      const response = await pRetry(
        async () => {
          const result = await pTimeout(
            this.client.post<DeepseekResponse>('/chat/completions', requestConfig),
            { milliseconds: this.config.timeout! }
          );
          return result;
        },
        {
          retries: 3,
          factor: 2,
          minTimeout: 1000,
          maxTimeout: 10000,
          onFailedAttempt: (error) => {
            console.warn(`Attempt ${error.attemptNumber} failed. ${error.retriesLeft} retries left.`);
          }
        }
      );

      const choice = response.data.choices[0];
      if (!choice) {
        throw new ProviderError('No response choice received from Deepseek API');
      }

      const message = choice.message;
      const toolCalls = message.tool_calls?.map(tc => ({
        id: tc.id,
        type: tc.type,
        function: {
          name: tc.function.name,
          arguments: tc.function.arguments
        }
      })) as ToolCall[] | undefined;

      return {
        id: response.data.id,
        role: 'assistant',
        content: message.content || '',
        toolCalls,
        timestamp: new Date()
      };

    } catch (error) {
      if (error instanceof ProviderError) {
        throw error;
      }
      
      throw new ProviderError(
        `Failed to generate response: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { originalError: error }
      );
    }
  }

  public async validateConnection(): Promise<boolean> {
    try {
      const response = await this.client.get('/models');
      return response.status === 200;
    } catch (error) {
      console.warn('Deepseek connection validation failed:', error);
      return false;
    }
  }

  public async getAvailableModels(): Promise<string[]> {
    try {
      const response = await this.client.get('/models');
      const models = response.data.data || [];
      return models
        .filter((model: any) => model.id.includes('deepseek'))
        .map((model: any) => model.id);
    } catch (error) {
      console.warn('Failed to fetch available models:', error);
      return ['deepseek-chat', 'deepseek-reasoner']; // Fallback to known models
    }
  }

  private formatMessages(messages: LLMMessage[]): any[] {
    return messages.map(msg => ({
      role: msg.role,
      content: msg.content,
      tool_calls: msg.toolCalls?.map(tc => ({
        id: tc.id,
        type: tc.type,
        function: {
          name: tc.function.name,
          arguments: tc.function.arguments
        }
      })),
      tool_call_id: msg.toolCallId
    }));
  }

  private formatTools(tools: ToolDefinition[]): any[] {
    return tools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters
      }
    }));
  }

  public updateConfig(newConfig: Partial<DeepseekConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Update client headers if API key changed
    if (newConfig.apiKey) {
      this.client.defaults.headers['Authorization'] = `Bearer ${newConfig.apiKey}`;
    }
    
    // Update base URL if changed
    if (newConfig.baseUrl) {
      this.client.defaults.baseURL = newConfig.baseUrl;
    }
    
    // Update timeout if changed
    if (newConfig.timeout) {
      this.client.defaults.timeout = newConfig.timeout;
    }
  }

  public getConfig(): DeepseekConfig {
    return { ...this.config };
  }
}
