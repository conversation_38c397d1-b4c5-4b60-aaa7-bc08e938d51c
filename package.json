{"name": "arien-ai-cli", "version": "1.0.0", "description": "Modern and powerful CLI terminal system with LLM integration and function calling capabilities", "type": "module", "main": "dist/index.js", "bin": {"arien": "dist/index.js"}, "scripts": {"build": "tsc && tsc-alias", "dev": "tsx src/index.ts", "start": "node dist/index.js", "watch": "tsc --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prepare": "npm run build", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["cli", "terminal", "ai", "llm", "function-calling", "shell", "typescript"], "author": "Arien AI", "license": "MIT", "engines": {"node": ">=20.0.0"}, "dependencies": {"@types/node": "^20.11.0", "ansi-escapes": "^6.2.0", "axios": "^1.6.7", "blessed": "^0.1.81", "blessed-contrib": "^4.11.0", "chalk": "^5.3.0", "commander": "^12.0.0", "conf": "^12.0.0", "date-fns": "^3.3.1", "execa": "^8.0.1", "inquirer": "^9.2.15", "nanoid": "^5.0.4", "ora": "^8.0.1", "p-retry": "^6.2.0", "p-timeout": "^6.1.2", "strip-ansi": "^7.1.0", "terminal-kit": "^3.0.1", "zod": "^3.22.4"}, "optionalDependencies": {"node-pty": "^1.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3.1.0", "@eslint/js": "^9.15.0", "@types/blessed": "^0.1.25", "@types/inquirer": "^9.0.7", "@types/jest": "^29.5.11", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "eslint": "^9.15.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "prettier": "^3.2.4", "rimraf": "^6.0.1", "ts-jest": "^29.1.2", "tsc-alias": "^1.8.16", "tsx": "^4.7.0", "typescript": "^5.8.3"}, "files": ["dist/**/*", "README.md", "LICENSE"], "repository": {"type": "git", "url": "https://github.com/arien-ai/arien-ai-cli.git"}, "bugs": {"url": "https://github.com/arien-ai/arien-ai-cli/issues"}, "homepage": "https://github.com/arien-ai/arien-ai-cli#readme"}