import ora, { Ora, Options } from 'ora';
import chalk from 'chalk';

export interface SpinnerOptions extends Partial<Options> {
  // Additional custom options can be added here if needed
}

export class ModernSpinner {
  private spinner: Ora;
  private static activeSpinners: Set<ModernSpinner> = new Set();

  constructor(options: SpinnerOptions = {}) {
    const defaultOptions: SpinnerOptions = {
      text: 'Processing...',
      color: 'cyan',
      spinner: 'dots12',
      indent: 0,
      interval: 80,
      isEnabled: true,
      isSilent: false,
      discardStdin: true,
      hideCursor: true,
      ...options
    };

    this.spinner = ora(defaultOptions);
    ModernSpinner.activeSpinners.add(this);
  }

  public start(text?: string): ModernSpinner {
    if (text) {
      this.spinner.text = text;
    }
    this.spinner.start();
    return this;
  }

  public stop(): ModernSpinner {
    this.spinner.stop();
    return this;
  }

  public succeed(text?: string): ModernSpinner {
    if (text) {
      this.spinner.text = text;
    }
    this.spinner.succeed();
    ModernSpinner.activeSpinners.delete(this);
    return this;
  }

  public fail(text?: string): ModernSpinner {
    if (text) {
      this.spinner.text = text;
    }
    this.spinner.fail();
    ModernSpinner.activeSpinners.delete(this);
    return this;
  }

  public warn(text?: string): ModernSpinner {
    if (text) {
      this.spinner.text = text;
    }
    this.spinner.warn();
    ModernSpinner.activeSpinners.delete(this);
    return this;
  }

  public info(text?: string): ModernSpinner {
    if (text) {
      this.spinner.text = text;
    }
    this.spinner.info();
    ModernSpinner.activeSpinners.delete(this);
    return this;
  }

  public setText(text: string): ModernSpinner {
    this.spinner.text = text;
    return this;
  }

  public setColor(color: SpinnerOptions['color']): ModernSpinner {
    if (color) {
      this.spinner.color = color;
    }
    return this;
  }

  public setSpinner(spinner: any): ModernSpinner {
    this.spinner.spinner = spinner;
    return this;
  }

  public get isSpinning(): boolean {
    return this.spinner.isSpinning;
  }

  public get text(): string {
    return this.spinner.text;
  }

  public static stopAll(): void {
    ModernSpinner.activeSpinners.forEach(spinner => {
      if (spinner.isSpinning) {
        spinner.stop();
      }
    });
    ModernSpinner.activeSpinners.clear();
  }

  public static createProcessingSpinner(text: string = 'Processing...'): ModernSpinner {
    return new ModernSpinner({
      text: chalk.cyan(text),
      color: 'cyan',
      spinner: 'dots12'
    });
  }

  public static createLoadingSpinner(text: string = 'Loading...'): ModernSpinner {
    return new ModernSpinner({
      text: chalk.blue(text),
      color: 'blue',
      spinner: 'bouncingBar'
    });
  }

  public static createThinkingSpinner(text: string = 'AI is thinking...'): ModernSpinner {
    return new ModernSpinner({
      text: chalk.magenta(text),
      color: 'magenta',
      spinner: 'mindblown'
    });
  }

  public static createExecutingSpinner(text: string = 'Executing command...'): ModernSpinner {
    return new ModernSpinner({
      text: chalk.yellow(text),
      color: 'yellow',
      spinner: 'runner'
    });
  }

  public static createConnectingSpinner(text: string = 'Connecting...'): ModernSpinner {
    return new ModernSpinner({
      text: chalk.green(text),
      color: 'green',
      spinner: 'dots'
    });
  }

  public static createToolExecutionSpinner(text: string = 'Executing tool...'): ModernSpinner {
    return new ModernSpinner({
      text: chalk.yellow(text),
      color: 'yellow',
      spinner: 'dots'
    });
  }

  public static createRetrySpinner(text: string = 'Retrying...'): ModernSpinner {
    return new ModernSpinner({
      text: chalk.yellow(text),
      color: 'yellow',
      spinner: 'dots'
    });
  }

  // Utility methods for common spinner patterns
  public static async withSpinner<T>(
    promise: Promise<T>,
    options: {
      text?: string;
      successText?: string;
      failText?: string;
      spinner?: ModernSpinner;
    } = {}
  ): Promise<T> {
    const spinner = options.spinner || ModernSpinner.createProcessingSpinner(options.text);
    
    try {
      spinner.start();
      const result = await promise;
      spinner.succeed(options.successText);
      return result;
    } catch (error) {
      spinner.fail(options.failText || `Failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    }
  }

  public static async withProgressSpinner<T>(
    promise: Promise<T>,
    progressCallback: (updateText: (text: string) => void) => void,
    options: {
      initialText?: string;
      successText?: string;
      failText?: string;
    } = {}
  ): Promise<T> {
    const spinner = ModernSpinner.createProcessingSpinner(options.initialText);
    
    try {
      spinner.start();
      
      // Set up progress callback
      const updateText = (text: string) => {
        spinner.setText(text);
      };
      
      progressCallback(updateText);
      
      const result = await promise;
      spinner.succeed(options.successText);
      return result;
    } catch (error) {
      spinner.fail(options.failText || `Failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    }
  }

  // Animation frames for custom spinners
  public static readonly CUSTOM_SPINNERS = {
    modernDots: {
      interval: 80,
      frames: ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏']
    },
    modernBars: {
      interval: 100,
      frames: ['▁', '▃', '▄', '▅', '▆', '▇', '█', '▇', '▆', '▅', '▄', '▃']
    },
    modernCircle: {
      interval: 120,
      frames: ['◐', '◓', '◑', '◒']
    },
    modernArrow: {
      interval: 100,
      frames: ['←', '↖', '↑', '↗', '→', '↘', '↓', '↙']
    },
    modernPulse: {
      interval: 200,
      frames: ['●', '○', '◉', '○']
    }
  };

  public static createCustomSpinner(
    spinnerName: keyof typeof ModernSpinner.CUSTOM_SPINNERS,
    text: string = 'Processing...',
    color: SpinnerOptions['color'] = 'cyan'
  ): ModernSpinner {
    const customSpinner = ModernSpinner.CUSTOM_SPINNERS[spinnerName];
    return new ModernSpinner({
      text,
      color,
      spinner: customSpinner
    });
  }
}
