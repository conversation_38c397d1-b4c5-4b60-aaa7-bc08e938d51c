import { EventEmitter } from 'events';
import { Tool<PERSON>all, ToolResult, ToolDefinition } from '@/types';
import { ModernSpinner } from './spinner';
import chalk from 'chalk';
import { nanoid } from 'nanoid';

export interface ToolExecutionOptions {
  parallel?: boolean;
  maxConcurrency?: number;
  timeout?: number;
  retryAttempts?: number;
  confirmBeforeExecution?: boolean;
  showProgress?: boolean;
}

export interface ToolExecutionContext {
  id: string;
  toolCall: ToolCall;
  startTime: Date;
  endTime?: Date;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  result?: ToolResult;
  error?: Error;
  retryCount: number;
  spinner?: any;
}

export interface ToolExecutionPlan {
  id: string;
  toolCalls: ToolCall[];
  executionOrder: ToolCall[][];
  estimatedDuration: number;
  riskLevel: 'low' | 'medium' | 'high';
  warnings: string[];
}

export class ToolCallsProcessor extends EventEmitter {
  private executionContexts: Map<string, ToolExecutionContext> = new Map();
  private toolDefinitions: Map<string, ToolDefinition> = new Map();
  private defaultOptions: Required<ToolExecutionOptions>;

  constructor(options: ToolExecutionOptions = {}) {
    super();
    
    this.defaultOptions = {
      parallel: false,
      maxConcurrency: 3,
      timeout: 300000, // 5 minutes
      retryAttempts: 2,
      confirmBeforeExecution: false,
      showProgress: true,
      ...options
    };
  }

  /**
   * Register tool definitions for better processing
   */
  public registerToolDefinition(definition: ToolDefinition): void {
    this.toolDefinitions.set(definition.name, definition);
  }

  /**
   * Create execution plan for tool calls
   */
  public createExecutionPlan(toolCalls: ToolCall[]): ToolExecutionPlan {
    const plan: ToolExecutionPlan = {
      id: nanoid(),
      toolCalls,
      executionOrder: [],
      estimatedDuration: 0,
      riskLevel: 'low',
      warnings: []
    };

    // Analyze tool calls for dependencies and risks
    const { order, warnings, riskLevel } = this.analyzeToolCalls(toolCalls);
    
    plan.executionOrder = order;
    plan.warnings = warnings;
    plan.riskLevel = riskLevel;
    plan.estimatedDuration = this.estimateExecutionTime(toolCalls);

    return plan;
  }

  /**
   * Analyze tool calls for execution order and risks
   */
  private analyzeToolCalls(toolCalls: ToolCall[]): {
    order: ToolCall[][];
    warnings: string[];
    riskLevel: 'low' | 'medium' | 'high';
  } {
    const warnings: string[] = [];
    let riskLevel: 'low' | 'medium' | 'high' = 'low';

    // Check for dangerous operations
    const dangerousTools = ['execute_shell_command'];
    const hasDangerousTools = toolCalls.some(call => 
      dangerousTools.includes(call.function.name)
    );

    if (hasDangerousTools) {
      riskLevel = 'high';
      warnings.push('⚠️  Dangerous operations detected. Review commands carefully.');
    }

    // Check for file system operations
    const fileSystemOperations = toolCalls.filter(call => {
      try {
        const args = JSON.parse(call.function.arguments);
        const command = args.command || '';
        return /rm|del|mv|cp|mkdir|rmdir/i.test(command);
      } catch {
        return false;
      }
    });

    if (fileSystemOperations.length > 0) {
      if (riskLevel === 'low') riskLevel = 'medium';
      warnings.push('📁 File system operations detected. Ensure you have backups.');
    }

    // Check for network operations
    const networkOperations = toolCalls.filter(call => {
      try {
        const args = JSON.parse(call.function.arguments);
        const command = args.command || '';
        return /curl|wget|ssh|scp|rsync|ping/i.test(command);
      } catch {
        return false;
      }
    });

    if (networkOperations.length > 0) {
      warnings.push('🌐 Network operations detected. Check connectivity and permissions.');
    }

    // Determine execution order
    const order = this.determineExecutionOrder(toolCalls);

    return { order, warnings, riskLevel };
  }

  /**
   * Determine optimal execution order
   */
  private determineExecutionOrder(toolCalls: ToolCall[]): ToolCall[][] {
    // For now, simple sequential execution
    // TODO: Implement dependency analysis for parallel execution
    
    if (this.defaultOptions.parallel) {
      // Group by parallelizable operations
      const groups: ToolCall[][] = [];
      let currentGroup: ToolCall[] = [];

      for (const toolCall of toolCalls) {
        const definition = this.toolDefinitions.get(toolCall.function.name);
        
        if (definition?.usage.parallel && currentGroup.length < this.defaultOptions.maxConcurrency) {
          currentGroup.push(toolCall);
        } else {
          if (currentGroup.length > 0) {
            groups.push([...currentGroup]);
            currentGroup = [];
          }
          currentGroup.push(toolCall);
        }
      }

      if (currentGroup.length > 0) {
        groups.push(currentGroup);
      }

      return groups;
    } else {
      // Sequential execution
      return toolCalls.map(call => [call]);
    }
  }

  /**
   * Estimate execution time
   */
  private estimateExecutionTime(toolCalls: ToolCall[]): number {
    let totalTime = 0;

    for (const toolCall of toolCalls) {
      const definition = this.toolDefinitions.get(toolCall.function.name);
      
      // Base estimation on tool type
      switch (toolCall.function.name) {
        case 'execute_shell_command':
          totalTime += 5000; // 5 seconds average
          break;
        default:
          totalTime += 2000; // 2 seconds default
      }
    }

    return totalTime;
  }

  /**
   * Execute tool calls according to plan
   */
  public async executeToolCalls(
    toolCalls: ToolCall[],
    executor: (toolCall: ToolCall) => Promise<ToolResult>,
    options: Partial<ToolExecutionOptions> = {}
  ): Promise<ToolResult[]> {
    const execOptions = { ...this.defaultOptions, ...options };
    const plan = this.createExecutionPlan(toolCalls);
    
    this.emit('execution-plan-created', plan);

    // Show warnings if any
    if (plan.warnings.length > 0) {
      this.emit('execution-warnings', plan.warnings);
    }

    // Request confirmation if needed
    if (execOptions.confirmBeforeExecution) {
      const confirmed = await this.requestConfirmation(plan);
      if (!confirmed) {
        throw new Error('Execution cancelled by user');
      }
    }

    const results: ToolResult[] = [];
    
    try {
      this.emit('execution-started', plan);

      for (const group of plan.executionOrder) {
        if (group.length === 1) {
          // Sequential execution
          const result = await this.executeSingleTool(group[0], executor, execOptions);
          results.push(result);
        } else {
          // Parallel execution
          const groupResults = await this.executeToolGroup(group, executor, execOptions);
          results.push(...groupResults);
        }
      }

      this.emit('execution-completed', { plan, results });
      return results;

    } catch (error) {
      this.emit('execution-failed', { plan, error, results });
      throw error;
    }
  }

  /**
   * Execute a single tool with full context tracking
   */
  private async executeSingleTool(
    toolCall: ToolCall,
    executor: (toolCall: ToolCall) => Promise<ToolResult>,
    options: Required<ToolExecutionOptions>
  ): Promise<ToolResult> {
    const context: ToolExecutionContext = {
      id: nanoid(),
      toolCall,
      startTime: new Date(),
      status: 'pending',
      retryCount: 0
    };

    this.executionContexts.set(context.id, context);

    try {
      context.status = 'running';
      this.emit('tool-execution-started', context);

      // Show progress spinner
      if (options.showProgress) {
        const toolName = chalk.cyan(toolCall.function.name);
        context.spinner = ModernSpinner.createToolExecutionSpinner(
          `Executing ${toolName}...`
        );
        context.spinner.start();
      }

      // Execute with timeout and retry logic
      const result = await this.executeWithRetry(toolCall, executor, options, context);
      
      context.status = 'completed';
      context.result = result;
      context.endTime = new Date();

      if (context.spinner) {
        if (result.success) {
          context.spinner.succeed(`${chalk.cyan(toolCall.function.name)} completed`);
        } else {
          context.spinner.fail(`${chalk.cyan(toolCall.function.name)} failed`);
        }
      }

      this.emit('tool-execution-completed', context);
      return result;

    } catch (error) {
      context.status = 'failed';
      context.error = error as Error;
      context.endTime = new Date();

      if (context.spinner) {
        context.spinner.fail(`${chalk.cyan(toolCall.function.name)} failed: ${error}`);
      }

      this.emit('tool-execution-failed', context);
      throw error;
    } finally {
      this.executionContexts.delete(context.id);
    }
  }

  /**
   * Execute tool group in parallel
   */
  private async executeToolGroup(
    toolCalls: ToolCall[],
    executor: (toolCall: ToolCall) => Promise<ToolResult>,
    options: Required<ToolExecutionOptions>
  ): Promise<ToolResult[]> {
    const promises = toolCalls.map(toolCall => 
      this.executeSingleTool(toolCall, executor, options)
    );

    return Promise.all(promises);
  }

  /**
   * Execute with retry logic
   */
  private async executeWithRetry(
    toolCall: ToolCall,
    executor: (toolCall: ToolCall) => Promise<ToolResult>,
    options: Required<ToolExecutionOptions>,
    context: ToolExecutionContext
  ): Promise<ToolResult> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= options.retryAttempts; attempt++) {
      try {
        context.retryCount = attempt;

        if (attempt > 0) {
          this.emit('tool-retry-attempt', { context, attempt });
          
          if (context.spinner) {
            context.spinner.setText(
              `Retrying ${chalk.cyan(toolCall.function.name)}... (Attempt ${attempt + 1})`
            );
          }

          // Exponential backoff
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
          await new Promise(resolve => setTimeout(resolve, delay));
        }

        // Execute with timeout
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error('Tool execution timeout')), options.timeout);
        });

        const result = await Promise.race([
          executor(toolCall),
          timeoutPromise
        ]);

        return result;

      } catch (error) {
        lastError = error as Error;
        
        // Check if error is retryable
        if (!this.isRetryableError(error as Error) || attempt === options.retryAttempts) {
          throw error;
        }
      }
    }

    throw lastError || new Error('Unknown execution error');
  }

  /**
   * Check if error is retryable
   */
  private isRetryableError(error: Error): boolean {
    const retryablePatterns = [
      /timeout/i,
      /network/i,
      /connection/i,
      /temporary/i,
      /rate limit/i
    ];

    return retryablePatterns.some(pattern => pattern.test(error.message));
  }

  /**
   * Request user confirmation
   */
  private async requestConfirmation(plan: ToolExecutionPlan): Promise<boolean> {
    return new Promise((resolve) => {
      this.emit('confirmation-required', {
        plan,
        callback: (confirmed: boolean) => resolve(confirmed)
      });
    });
  }

  /**
   * Format tool call for display
   */
  public formatToolCall(toolCall: ToolCall): string {
    const functionName = chalk.cyan.bold(toolCall.function.name);
    
    try {
      const args = JSON.parse(toolCall.function.arguments);
      const formattedArgs = Object.entries(args)
        .map(([key, value]) => `${chalk.yellow(key)}: ${chalk.white(JSON.stringify(value))}`)
        .join(', ');
      
      return `🔧 ${functionName}(${formattedArgs})`;
    } catch {
      return `🔧 ${functionName}(${chalk.gray(toolCall.function.arguments)})`;
    }
  }

  /**
   * Format tool result for display
   */
  public formatToolResult(result: ToolResult): string {
    const status = result.success ? 
      chalk.green('✅ SUCCESS') : 
      chalk.red('❌ FAILED');
    
    const executionTime = chalk.gray(`(${result.executionTime}ms)`);
    const toolId = chalk.gray(`[${result.toolCallId.slice(0, 8)}]`);

    let output = `${status} ${toolId} ${executionTime}\n`;
    
    if (result.success) {
      output += this.formatSuccessOutput(result.result);
    } else {
      output += this.formatErrorOutput(result.error || result.result);
    }

    return output;
  }

  /**
   * Format success output
   */
  private formatSuccessOutput(output: string): string {
    const lines = output.split('\n');
    return lines.map(line => {
      if (line.trim() === '') return line;
      return chalk.gray('│ ') + line;
    }).join('\n');
  }

  /**
   * Format error output
   */
  private formatErrorOutput(error: string): string {
    const lines = error.split('\n');
    return lines.map(line => {
      if (line.trim() === '') return line;
      return chalk.red('│ ') + chalk.red(line);
    }).join('\n');
  }

  /**
   * Get execution statistics
   */
  public getExecutionStats(): {
    totalExecutions: number;
    successRate: number;
    averageExecutionTime: number;
    mostUsedTools: Array<{ name: string; count: number }>;
  } {
    // This would be implemented with persistent storage
    // For now, return empty stats
    return {
      totalExecutions: 0,
      successRate: 0,
      averageExecutionTime: 0,
      mostUsedTools: []
    };
  }

  /**
   * Cancel all running executions
   */
  public cancelAllExecutions(): void {
    for (const context of this.executionContexts.values()) {
      if (context.status === 'running') {
        context.status = 'cancelled';
        if (context.spinner) {
          context.spinner.fail(`${chalk.cyan(context.toolCall.function.name)} cancelled`);
        }
        this.emit('tool-execution-cancelled', context);
      }
    }
    
    this.executionContexts.clear();
  }

  /**
   * Get active executions
   */
  public getActiveExecutions(): ToolExecutionContext[] {
    return Array.from(this.executionContexts.values())
      .filter(context => context.status === 'running');
  }
}
