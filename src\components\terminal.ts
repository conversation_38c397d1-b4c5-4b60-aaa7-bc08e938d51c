import { TerminalLayout } from './terminal-layout.js';
import { TerminalEngine } from '../core/terminal-engine.js';
import { OnboardingFlow } from './onboarding.js';
import { ConfigManager } from '../core/config.js';
import { TerminalEvent, LLMMessage } from '../types/index.js';
import chalk from 'chalk';
import { ModernSpinner } from './spinner.js';

export class Terminal {
  private layout: TerminalLayout;
  private engine: TerminalEngine;
  private configManager: ConfigManager;
  private isInitialized: boolean = false;

  constructor() {
    this.configManager = ConfigManager.getInstance();
    this.engine = new TerminalEngine();
    this.layout = new TerminalLayout({
      title: 'Arien AI CLI',
      border: true,
      scrollable: true,
      mouse: true,
      keys: true
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    // Engine events
    this.engine.on('message', (event: TerminalEvent) => {
      this.handleMessage(event.data as LLMMessage);
    });

    this.engine.on('state-change', (state) => {
      this.layout.updateHeader(state);
      
      if (state.isProcessing) {
        this.layout.showSpinner(state.currentCommand || 'Processing...');
      } else {
        this.layout.hideSpinner();
      }
    });

    this.engine.on('error', (error: Error) => {
      this.layout.showError(error.message);
      this.layout.hideSpinner();
    });

    this.engine.on('tool-execution-start', ({ toolCall }) => {
      this.layout.showSpinner(`Executing: ${toolCall.function.name}`);
      this.layout.addMessage('system', `🔧 Executing tool: ${toolCall.function.name}`, new Date());
    });

    this.engine.on('tool-execution-complete', ({ toolCall, result }) => {
      const status = result.success ? '✅' : '❌';
      const message = `${status} Tool ${toolCall.function.name} completed in ${result.executionTime}ms`;
      this.layout.addMessage('system', message, new Date());
    });

    // Layout events
    this.layout.getScreen().on('user-input', async (input: string) => {
      await this.handleUserInput(input);
    });

    // Global error handling
    process.on('uncaughtException', (error) => {
      this.layout.showError(`Uncaught exception: ${error.message}`);
      console.error('Uncaught exception:', error);
    });

    process.on('unhandledRejection', (reason) => {
      this.layout.showError(`Unhandled rejection: ${reason}`);
      console.error('Unhandled rejection:', reason);
    });
  }

  private handleMessage(message: LLMMessage): void {
    this.layout.addMessage(message.role, message.content, message.timestamp);
  }

  private async handleUserInput(input: string): Promise<void> {
    if (!input.trim()) return;

    // Show user message immediately
    this.layout.addMessage('user', input, new Date());

    try {
      await this.engine.processUserInput(input);
    } catch (error) {
      this.layout.showError(error instanceof Error ? error.message : 'Unknown error');
    }
  }

  public async start(): Promise<void> {
    try {
      // Show welcome message
      this.showWelcome();

      // Check if configuration exists
      if (!this.configManager.isConfigured()) {
        this.layout.showInfo('Configuration not found. Starting onboarding...');
        await this.runOnboarding();
      }

      // Initialize engine
      await this.initializeEngine();

      // Show ready message
      this.showReadyMessage();

      // Start the terminal interface
      this.layout.focusInput();
      this.layout.render();

      this.isInitialized = true;

    } catch (error) {
      this.layout.showError(`Failed to start terminal: ${error instanceof Error ? error.message : 'Unknown error'}`);
      process.exit(1);
    }
  }

  private showWelcome(): void {
    this.layout.addMessage('system', '🚀 Welcome to Arien AI CLI!', new Date());
    this.layout.addMessage('system', 'A powerful terminal interface with AI assistance and function calling.', new Date());
    this.layout.addMessage('system', '', new Date());
  }

  private async runOnboarding(): Promise<void> {
    const spinner = ModernSpinner.createLoadingSpinner('Starting onboarding...');
    
    try {
      spinner.start();
      
      // Hide the terminal layout temporarily
      this.layout.getScreen().destroy();
      
      // Run onboarding in console mode
      const onboarding = new OnboardingFlow();
      const result = await onboarding.start();

      if (!result.success) {
        console.log(chalk.red('Onboarding failed. Exiting...'));
        process.exit(1);
      }

      // Check if user chose not to auto-start
      if (result.autoStart === false) {
        console.log(chalk.cyan('Configuration saved. Run "arien" to start the interactive terminal.'));
        process.exit(0);
      }
      
      // Recreate layout
      this.layout = new TerminalLayout({
        title: 'Arien AI CLI',
        border: true,
        scrollable: true,
        mouse: true,
        keys: true
      });
      
      this.setupEventHandlers();
      
      spinner.succeed('Onboarding completed successfully!');
      
    } catch (error) {
      spinner.fail('Onboarding failed');
      throw error;
    }
  }

  private async initializeEngine(): Promise<void> {
    const spinner = ModernSpinner.createConnectingSpinner('Initializing AI engine...');
    
    try {
      spinner.start();
      await this.engine.initialize();
      spinner.succeed('AI engine initialized');
    } catch (error) {
      spinner.fail('Failed to initialize AI engine');
      this.layout.showWarning('AI engine initialization failed. Some features may not work properly.');
    }
  }

  private showReadyMessage(): void {
    const config = this.configManager.getAll();
    
    this.layout.addMessage('system', '✅ Terminal is ready!', new Date());
    this.layout.addMessage('system', `Provider: ${config.provider} | Model: ${config.model}`, new Date());
    this.layout.addMessage('system', `Working Directory: ${config.workingDirectory}`, new Date());
    this.layout.addMessage('system', '', new Date());
    this.layout.addMessage('system', 'Type your message or use slash commands:', new Date());
    this.layout.addMessage('system', '  /help - Show available commands', new Date());
    this.layout.addMessage('system', '  /model - Switch AI model', new Date());
    this.layout.addMessage('system', '  /session - Manage sessions', new Date());
    this.layout.addMessage('system', '  /config - View configuration', new Date());
    this.layout.addMessage('system', '', new Date());
    
    this.layout.updateStatus('Ready - Type your message or command');
  }

  public async quickStart(): Promise<void> {
    // Quick start mode for development/testing
    try {
      this.showWelcome();
      
      if (!this.configManager.isConfigured()) {
        this.layout.showInfo('Quick start mode - using minimal configuration');
        
        // Try to use environment variables or prompt for minimal config
        const onboarding = new OnboardingFlow();
        const result = await onboarding.quickSetup();
        
        if (!result.success) {
          this.layout.showError('Quick setup failed. Please run full onboarding.');
          return;
        }
      }
      
      await this.initializeEngine();
      this.showReadyMessage();
      
      this.layout.focusInput();
      this.layout.render();
      
      this.isInitialized = true;
      
    } catch (error) {
      this.layout.showError(`Quick start failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  public async restart(): Promise<void> {
    if (this.isInitialized) {
      this.layout.clearChat();
      this.layout.showInfo('Restarting terminal...');
      
      try {
        await this.engine.initialize();
        this.showReadyMessage();
        this.layout.updateStatus('Ready - Terminal restarted');
      } catch (error) {
        this.layout.showError(`Restart failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
  }

  public async reconfigure(): Promise<void> {
    this.layout.showInfo('Starting reconfiguration...');
    
    try {
      // Destroy current layout
      this.layout.getScreen().destroy();
      
      // Run onboarding
      const onboarding = new OnboardingFlow();
      const result = await onboarding.start();
      
      if (result.success) {
        // Recreate layout and restart
        this.layout = new TerminalLayout({
          title: 'Arien AI CLI',
          border: true,
          scrollable: true,
          mouse: true,
          keys: true
        });
        
        this.setupEventHandlers();
        await this.initializeEngine();
        this.showReadyMessage();
        
        this.layout.focusInput();
        this.layout.render();
      } else {
        console.log(chalk.red('Reconfiguration failed.'));
        process.exit(1);
      }
      
    } catch (error) {
      console.error('Reconfiguration error:', error);
      process.exit(1);
    }
  }

  public getEngine(): TerminalEngine {
    return this.engine;
  }

  public getLayout(): TerminalLayout {
    return this.layout;
  }

  public isReady(): boolean {
    return this.isInitialized && this.engine.isReady();
  }

  public destroy(): void {
    if (this.layout) {
      this.layout.destroy();
    }
    
    if (this.engine) {
      this.engine.destroy();
    }
    
    ModernSpinner.stopAll();
  }

  // Utility methods for external control
  public async executeCommand(command: string): Promise<void> {
    if (this.isReady()) {
      await this.engine.processUserInput(command);
    } else {
      throw new Error('Terminal is not ready');
    }
  }

  public addSystemMessage(message: string): void {
    this.layout.addMessage('system', message, new Date());
  }

  public showStatus(message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info'): void {
    this.layout.updateStatus(message, type);
  }
}
