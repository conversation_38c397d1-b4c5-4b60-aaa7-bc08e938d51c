import { EventEmitter } from 'events';
import { ModernSpinner } from '@/components/spinner';
import chalk from 'chalk';

export interface RetryOptions {
  maxAttempts?: number;
  baseDelay?: number;
  maxDelay?: number;
  backoffFactor?: number;
  jitter?: boolean;
  retryCondition?: (error: Error, attempt: number) => boolean;
  onRetry?: (error: Error, attempt: number) => void;
  timeout?: number;
}

export interface RetryContext {
  attempt: number;
  totalAttempts: number;
  lastError: Error | null;
  startTime: Date;
  delays: number[];
  isRetryable: boolean;
}

export class RetryLogic extends EventEmitter {
  private defaultOptions: Required<Omit<RetryOptions, 'retryCondition' | 'onRetry'>>;

  constructor() {
    super();
    
    this.defaultOptions = {
      maxAttempts: 3,
      baseDelay: 1000,
      maxDelay: 30000,
      backoffFactor: 2,
      jitter: true,
      timeout: 300000 // 5 minutes
    };
  }

  /**
   * Execute function with retry logic
   */
  public async executeWithRetry<T>(
    fn: () => Promise<T>,
    options: RetryOptions = {}
  ): Promise<T> {
    const opts = { ...this.defaultOptions, ...options };
    const context: RetryContext = {
      attempt: 0,
      totalAttempts: opts.maxAttempts,
      lastError: null,
      startTime: new Date(),
      delays: [],
      isRetryable: true
    };

    let spinner: any = null;

    try {
      for (let attempt = 1; attempt <= opts.maxAttempts; attempt++) {
        context.attempt = attempt;
        
        try {
          // Show retry spinner for attempts > 1
          if (attempt > 1) {
            spinner = ModernSpinner.createRetrySpinner(
              `Retrying operation... (attempt ${attempt})`
            );
            spinner.start();
            
            this.emit('retry-attempt', { context, attempt });
            
            // Calculate delay with exponential backoff
            const delay = this.calculateDelay(attempt - 1, opts);
            context.delays.push(delay);
            
            await this.delay(delay);
          }

          // Execute the function with timeout
          const result = await this.executeWithTimeout(fn, opts.timeout);
          
          if (spinner) {
            spinner.succeed(`Operation succeeded on attempt ${attempt}`);
          }

          this.emit('retry-success', { context, result, attempt });
          return result;

        } catch (error) {
          context.lastError = error as Error;
          
          if (spinner) {
            spinner.stop();
          }

          // Check if we should retry
          const shouldRetry = this.shouldRetry(error as Error, attempt, opts);
          context.isRetryable = shouldRetry;

          if (!shouldRetry || attempt === opts.maxAttempts) {
            if (spinner) {
              spinner.fail(`Operation failed after ${attempt} attempts`);
            }
            
            this.emit('retry-failed', { context, error, attempt });
            throw error;
          }

          // Call retry callback if provided
          if (options.onRetry) {
            options.onRetry(error as Error, attempt);
          }

          this.emit('retry-error', { context, error, attempt });
        }
      }

      // This should never be reached, but TypeScript requires it
      throw context.lastError || new Error('Unknown retry error');

    } catch (error) {
      if (spinner && spinner.isSpinning) {
        spinner.fail('Operation failed');
      }
      throw error;
    }
  }

  /**
   * Execute function with timeout
   */
  private async executeWithTimeout<T>(
    fn: () => Promise<T>,
    timeout: number
  ): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Operation timed out after ${timeout}ms`));
      }, timeout);

      fn()
        .then(result => {
          clearTimeout(timeoutId);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timeoutId);
          reject(error);
        });
    });
  }

  /**
   * Calculate delay with exponential backoff and jitter
   */
  private calculateDelay(attempt: number, options: Required<Omit<RetryOptions, 'retryCondition' | 'onRetry'>>): number {
    let delay = options.baseDelay * Math.pow(options.backoffFactor, attempt);
    
    // Apply maximum delay limit
    delay = Math.min(delay, options.maxDelay);
    
    // Add jitter to prevent thundering herd
    if (options.jitter) {
      delay = delay * (0.5 + Math.random() * 0.5);
    }
    
    return Math.floor(delay);
  }

  /**
   * Determine if error should trigger a retry
   */
  private shouldRetry(error: Error, attempt: number, options: RetryOptions): boolean {
    // Use custom retry condition if provided
    if (options.retryCondition) {
      return options.retryCondition(error, attempt);
    }

    // Default retry conditions
    return this.isRetryableError(error);
  }

  /**
   * Check if error is retryable by default
   */
  private isRetryableError(error: Error): boolean {
    const retryablePatterns = [
      // Network errors
      /ECONNRESET/i,
      /ECONNREFUSED/i,
      /ETIMEDOUT/i,
      /ENOTFOUND/i,
      /socket hang up/i,
      /network timeout/i,
      
      // HTTP errors
      /502 Bad Gateway/i,
      /503 Service Unavailable/i,
      /504 Gateway Timeout/i,
      /429 Too Many Requests/i,
      
      // Rate limiting
      /rate limit/i,
      /quota exceeded/i,
      /throttled/i,
      
      // Temporary failures
      /temporary/i,
      /temporarily unavailable/i,
      /try again/i,
      
      // Provider-specific errors
      /model overloaded/i,
      /server overloaded/i,
      /capacity/i
    ];

    const errorMessage = error.message || error.toString();
    return retryablePatterns.some(pattern => pattern.test(errorMessage));
  }

  /**
   * Simple delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Create retry strategy for specific scenarios
   */
  public static createNetworkRetryStrategy(): RetryOptions {
    return {
      maxAttempts: 5,
      baseDelay: 1000,
      maxDelay: 30000,
      backoffFactor: 2,
      jitter: true,
      retryCondition: (error: Error) => {
        const networkErrors = [
          /ECONNRESET/i,
          /ECONNREFUSED/i,
          /ETIMEDOUT/i,
          /ENOTFOUND/i,
          /socket hang up/i,
          /network timeout/i
        ];
        
        return networkErrors.some(pattern => pattern.test(error.message));
      }
    };
  }

  /**
   * Create retry strategy for rate limiting
   */
  public static createRateLimitRetryStrategy(): RetryOptions {
    return {
      maxAttempts: 10,
      baseDelay: 2000,
      maxDelay: 60000,
      backoffFactor: 1.5,
      jitter: true,
      retryCondition: (error: Error) => {
        const rateLimitErrors = [
          /429/i,
          /rate limit/i,
          /quota exceeded/i,
          /throttled/i,
          /too many requests/i
        ];
        
        return rateLimitErrors.some(pattern => pattern.test(error.message));
      }
    };
  }

  /**
   * Create retry strategy for LLM provider errors
   */
  public static createLLMProviderRetryStrategy(): RetryOptions {
    return {
      maxAttempts: 3,
      baseDelay: 2000,
      maxDelay: 15000,
      backoffFactor: 2,
      jitter: true,
      retryCondition: (error: Error, attempt: number) => {
        // Don't retry on authentication errors
        if (/401|403|unauthorized|forbidden/i.test(error.message)) {
          return false;
        }

        // Don't retry on invalid input errors
        if (/400|bad request|invalid/i.test(error.message)) {
          return false;
        }

        // Retry on server errors and rate limits
        const retryableErrors = [
          /5\d\d/i, // 5xx errors
          /429/i,   // Rate limit
          /rate limit/i,
          /overloaded/i,
          /capacity/i,
          /timeout/i,
          /temporary/i
        ];

        return retryableErrors.some(pattern => pattern.test(error.message));
      }
    };
  }

  /**
   * Create retry strategy for shell commands
   */
  public static createShellCommandRetryStrategy(): RetryOptions {
    return {
      maxAttempts: 2,
      baseDelay: 500,
      maxDelay: 5000,
      backoffFactor: 2,
      jitter: false,
      retryCondition: (error: Error) => {
        // Only retry on specific temporary failures
        const retryableErrors = [
          /resource temporarily unavailable/i,
          /device or resource busy/i,
          /operation timed out/i,
          /connection refused/i
        ];

        return retryableErrors.some(pattern => pattern.test(error.message));
      }
    };
  }

  /**
   * Retry with exponential backoff (utility function)
   */
  public static async withExponentialBackoff<T>(
    fn: () => Promise<T>,
    maxAttempts: number = 3,
    baseDelay: number = 1000
  ): Promise<T> {
    const retryLogic = new RetryLogic();
    return retryLogic.executeWithRetry(fn, {
      maxAttempts,
      baseDelay,
      backoffFactor: 2,
      jitter: true
    });
  }

  /**
   * Retry with linear backoff (utility function)
   */
  public static async withLinearBackoff<T>(
    fn: () => Promise<T>,
    maxAttempts: number = 3,
    delay: number = 1000
  ): Promise<T> {
    const retryLogic = new RetryLogic();
    return retryLogic.executeWithRetry(fn, {
      maxAttempts,
      baseDelay: delay,
      backoffFactor: 1,
      jitter: false
    });
  }

  /**
   * Retry with custom condition (utility function)
   */
  public static async withCustomCondition<T>(
    fn: () => Promise<T>,
    retryCondition: (error: Error, attempt: number) => boolean,
    maxAttempts: number = 3
  ): Promise<T> {
    const retryLogic = new RetryLogic();
    return retryLogic.executeWithRetry(fn, {
      maxAttempts,
      retryCondition
    });
  }

  /**
   * Format retry context for logging
   */
  public static formatRetryContext(context: RetryContext): string {
    const duration = new Date().getTime() - context.startTime.getTime();
    const avgDelay = context.delays.length > 0 ? 
      context.delays.reduce((a, b) => a + b, 0) / context.delays.length : 0;

    return [
      chalk.blue(`Attempt: ${context.attempt}/${context.totalAttempts}`),
      chalk.yellow(`Duration: ${duration}ms`),
      chalk.gray(`Avg Delay: ${Math.round(avgDelay)}ms`),
      context.lastError ? chalk.red(`Last Error: ${context.lastError.message}`) : ''
    ].filter(Boolean).join(' | ');
  }
}
