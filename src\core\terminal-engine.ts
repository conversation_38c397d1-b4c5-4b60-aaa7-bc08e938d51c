import { EventEmitter } from 'events';
import { TerminalState, TerminalSession, LLMMessage, ToolCall, ToolResult, TerminalEvent } from '../types/index.js';
import { ConfigManager } from './config.js';
import { SessionManager } from './session-manager.js';
import { LLMManager } from './llm-manager.js';
import { ToolManager } from './tool-manager.js';
import { ModernSpinner } from '../components/spinner.js';
import { SlashCommandManager } from '../components/slash-commands.js';
import { nanoid } from 'nanoid';
import chalk from 'chalk';

export class TerminalEngine extends EventEmitter {
  private state: TerminalState;
  private configManager: ConfigManager;
  private sessionManager: SessionManager;
  private llmManager: LLMManager;
  private toolManager: ToolManager;
  private slashCommandManager: SlashCommandManager;
  private isProcessing: boolean = false;

  constructor() {
    super();
    
    this.configManager = ConfigManager.getInstance();
    this.sessionManager = new SessionManager();
    this.llmManager = new LLMManager();
    this.toolManager = new ToolManager();
    
    this.slashCommandManager = new SlashCommandManager(
      this.configManager,
      this.sessionManager,
      this.llmManager
    );

    this.state = {
      currentSession: null,
      sessions: [],
      config: this.configManager.getAll(),
      isProcessing: false,
      currentCommand: '',
      messageHistory: []
    };

    this.initializeState();
  }

  private initializeState(): void {
    // Load existing sessions
    this.state.sessions = this.sessionManager.getAllSessions();
    
    // Set current session if configured
    const sessionId = this.configManager.get('sessionId');
    if (sessionId) {
      try {
        this.state.currentSession = this.sessionManager.setCurrentSession(sessionId);
      } catch {
        // Session not found, create new one
        this.state.currentSession = this.sessionManager.createSession();
      }
    } else {
      // Create initial session
      this.state.currentSession = this.sessionManager.createSession();
    }

    this.state.messageHistory = this.sessionManager.getMessages();
  }

  public async processUserInput(input: string): Promise<void> {
    if (this.isProcessing) {
      this.emit('error', new Error('Already processing a request. Please wait.'));
      return;
    }

    try {
      this.isProcessing = true;
      this.state.isProcessing = true;
      this.state.currentCommand = input;
      this.emitStateChange();

      // Check if it's a slash command
      if (input.startsWith('/')) {
        const handled = await this.slashCommandManager.executeCommand(input);
        if (handled) {
          return;
        }
      }

      // Process as regular chat message
      await this.processChatMessage(input);

    } catch (error) {
      this.emit('error', error);
    } finally {
      this.isProcessing = false;
      this.state.isProcessing = false;
      this.state.currentCommand = '';
      this.emitStateChange();
    }
  }

  private async processChatMessage(input: string): Promise<void> {
    // Create user message
    const userMessage: LLMMessage = {
      id: nanoid(),
      role: 'user',
      content: input,
      timestamp: new Date()
    };

    // Add to session
    this.sessionManager.addMessage(userMessage);
    this.updateMessageHistory();
    this.emitMessage(userMessage);

    // Get conversation context
    const messages = this.getConversationContext();
    
    // Get tool definitions
    const tools = this.toolManager.getToolDefinitions();

    // Generate AI response
    const spinner = ModernSpinner.createThinkingSpinner('AI is thinking...');
    
    try {
      spinner.start();
      
      const aiResponse = await this.llmManager.generateResponse(messages, tools);
      
      spinner.succeed('Response generated');
      
      // Add AI response to session
      this.sessionManager.addMessage(aiResponse);
      this.updateMessageHistory();
      this.emitMessage(aiResponse);

      // Process tool calls if any
      if (aiResponse.toolCalls && aiResponse.toolCalls.length > 0) {
        await this.processToolCalls(aiResponse.toolCalls);
      }

    } catch (error) {
      spinner.fail('Failed to generate response');
      
      const errorMessage: LLMMessage = {
        id: nanoid(),
        role: 'system',
        content: `Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
        timestamp: new Date()
      };
      
      this.sessionManager.addMessage(errorMessage);
      this.updateMessageHistory();
      this.emitMessage(errorMessage);
    }
  }

  private async processToolCalls(toolCalls: ToolCall[]): Promise<void> {
    const spinner = ModernSpinner.createExecutingSpinner('Executing tools...');
    
    try {
      spinner.start();
      
      // Execute tool calls
      const toolResults: ToolResult[] = [];
      
      for (const toolCall of toolCalls) {
        this.emit('tool-execution-start', { toolCall });
        
        const result = await this.toolManager.executeToolCall(toolCall);
        toolResults.push(result);
        
        this.emit('tool-execution-complete', { toolCall, result });
        
        // Create tool result message
        const toolMessage: LLMMessage = {
          id: nanoid(),
          role: 'tool',
          content: result.result,
          toolCallId: result.toolCallId,
          timestamp: new Date()
        };
        
        this.sessionManager.addMessage(toolMessage);
        this.updateMessageHistory();
        this.emitMessage(toolMessage);
      }
      
      spinner.succeed(`Executed ${toolResults.length} tool(s)`);
      
      // Generate follow-up response if needed
      const hasFailures = toolResults.some(r => !r.success);
      if (hasFailures || toolResults.some(r => r.result.trim())) {
        await this.generateFollowUpResponse();
      }
      
    } catch (error) {
      spinner.fail('Tool execution failed');
      throw error;
    }
  }

  private async generateFollowUpResponse(): Promise<void> {
    const spinner = ModernSpinner.createThinkingSpinner('Generating follow-up response...');
    
    try {
      spinner.start();
      
      const messages = this.getConversationContext();
      const tools = this.toolManager.getToolDefinitions();
      
      const followUpResponse = await this.llmManager.generateResponse(messages, tools);
      
      spinner.succeed('Follow-up response generated');
      
      this.sessionManager.addMessage(followUpResponse);
      this.updateMessageHistory();
      this.emitMessage(followUpResponse);
      
      // Handle recursive tool calls if any
      if (followUpResponse.toolCalls && followUpResponse.toolCalls.length > 0) {
        await this.processToolCalls(followUpResponse.toolCalls);
      }
      
    } catch (error) {
      spinner.fail('Failed to generate follow-up response');
      console.error('Follow-up response error:', error);
    }
  }

  private getConversationContext(): LLMMessage[] {
    const messages = this.sessionManager.getMessages();
    
    // Add system prompt
    const systemPrompt: LLMMessage = {
      id: nanoid(),
      role: 'system',
      content: this.getSystemPrompt(),
      timestamp: new Date()
    };
    
    return [systemPrompt, ...messages];
  }

  private getSystemPrompt(): string {
    const config = this.configManager.getAll();
    
    return `You are Arien AI, a powerful CLI assistant that can help users with various tasks through natural language interaction and function calling.

CORE CAPABILITIES:
- Execute shell commands and system operations
- File and directory management
- Process management and system monitoring
- Development tasks (git, npm, build tools, etc.)
- Network operations and API testing
- Data processing and analysis

IMPORTANT GUIDELINES:
1. Always be helpful, accurate, and safe
2. Ask for confirmation before executing potentially destructive commands
3. Provide clear explanations of what you're doing
4. Use appropriate tools for each task
5. Handle errors gracefully and provide helpful feedback
6. Respect user's working directory and environment

FUNCTION CALLING:
- Use the execute_shell_command tool for system operations
- Always validate commands before execution
- Provide detailed output and error handling
- Use sequential execution for dependent operations
- Use parallel execution for independent operations

CURRENT CONTEXT:
- Working Directory: ${config.workingDirectory}
- Provider: ${config.provider}
- Model: ${config.model}
- Auto-approve: ${config.autoApprove ? 'enabled' : 'disabled'}

Remember to be conversational, helpful, and always prioritize user safety and system integrity.`;
  }

  private updateMessageHistory(): void {
    this.state.messageHistory = this.sessionManager.getMessages();
    this.emitStateChange();
  }

  private emitMessage(message: LLMMessage): void {
    const event: TerminalEvent = {
      type: 'message',
      data: message,
      timestamp: new Date()
    };
    
    this.emit('message', event);
  }

  private emitStateChange(): void {
    this.emit('state-change', this.state);
  }

  // Public API methods
  public getState(): TerminalState {
    return { ...this.state };
  }

  public getCurrentSession(): TerminalSession | null {
    return this.state.currentSession;
  }

  public createNewSession(name?: string): TerminalSession {
    const session = this.sessionManager.createSession(name);
    this.state.currentSession = session;
    this.state.sessions = this.sessionManager.getAllSessions();
    this.updateMessageHistory();
    this.emitStateChange();
    return session;
  }

  public switchSession(sessionId: string): TerminalSession {
    const session = this.sessionManager.setCurrentSession(sessionId);
    this.state.currentSession = session;
    this.updateMessageHistory();
    this.emitStateChange();
    return session;
  }

  public deleteSession(sessionId: string): boolean {
    const success = this.sessionManager.deleteSession(sessionId);
    if (success) {
      this.state.sessions = this.sessionManager.getAllSessions();
      
      // If current session was deleted, create a new one
      if (this.state.currentSession?.id === sessionId) {
        this.state.currentSession = this.sessionManager.createSession();
        this.updateMessageHistory();
      }
      
      this.emitStateChange();
    }
    return success;
  }

  public clearCurrentSession(): void {
    this.sessionManager.clearMessages();
    this.updateMessageHistory();
    this.emitStateChange();
  }

  public async switchProvider(provider: 'deepseek' | 'ollama', model?: string): Promise<void> {
    await this.llmManager.switchProvider(provider, model);
    this.state.config = this.configManager.getAll();
    this.emitStateChange();
  }

  public async switchModel(model: string): Promise<void> {
    await this.llmManager.switchModel(model);
    this.state.config = this.configManager.getAll();
    this.emitStateChange();
  }

  public setWorkingDirectory(directory: string): void {
    this.configManager.set('workingDirectory', directory);
    this.toolManager.setWorkingDirectory(directory);
    this.state.config = this.configManager.getAll();
    this.emitStateChange();
  }

  public getAvailableCommands(): string[] {
    return this.slashCommandManager.getCommands().map(cmd => `/${cmd.name}`);
  }

  public isReady(): boolean {
    return this.configManager.isConfigured() && !this.isProcessing;
  }

  public async initialize(): Promise<void> {
    if (this.configManager.isConfigured()) {
      try {
        await this.llmManager.initializeProvider();
      } catch (error) {
        console.warn('Failed to initialize LLM provider:', error);
      }
    }
  }

  public destroy(): void {
    this.removeAllListeners();
    ModernSpinner.stopAll();
  }
}
