import { ToolDefinition, ToolCall, ToolResult, LLMMessage, CommandError } from '../types/index.js';
import { ShellTool } from '../tools/shell.js';
import { nanoid } from 'nanoid';

export class ToolManager {
  private tools: Map<string, any> = new Map();
  private shellTool: ShellTool;

  constructor(workingDirectory?: string) {
    this.shellTool = new ShellTool(workingDirectory);
    this.registerDefaultTools();
  }

  private registerDefaultTools(): void {
    this.registerTool('execute_shell_command', this.shellTool);
  }

  public registerTool(name: string, tool: any): void {
    this.tools.set(name, tool);
  }

  public getToolDefinitions(): ToolDefinition[] {
    const definitions: ToolDefinition[] = [];
    
    this.tools.forEach((tool, name) => {
      if (typeof tool.getToolDefinition === 'function') {
        definitions.push(tool.getToolDefinition());
      }
    });

    return definitions;
  }

  public getToolDefinition(name: string): ToolDefinition | null {
    const tool = this.tools.get(name);
    if (tool && typeof tool.getToolDefinition === 'function') {
      return tool.getToolDefinition();
    }
    return null;
  }

  public async executeToolCall(toolCall: ToolCall): Promise<ToolResult> {
    const startTime = Date.now();
    const toolName = toolCall.function.name;
    const tool = this.tools.get(toolName);

    if (!tool) {
      return {
        toolCallId: toolCall.id,
        result: `Error: Tool '${toolName}' not found`,
        success: false,
        error: `Tool '${toolName}' is not registered`,
        executionTime: Date.now() - startTime
      };
    }

    try {
      let args: any;
      try {
        args = JSON.parse(toolCall.function.arguments);
      } catch (parseError) {
        return {
          toolCallId: toolCall.id,
          result: `Error: Invalid JSON arguments for tool '${toolName}'`,
          success: false,
          error: `Failed to parse arguments: ${parseError instanceof Error ? parseError.message : 'Unknown error'}`,
          executionTime: Date.now() - startTime
        };
      }

      let result: any;

      // Handle different tool types
      if (toolName === 'execute_shell_command') {
        result = await this.executeShellCommand(args);
      } else if (typeof tool.execute === 'function') {
        result = await tool.execute(args);
      } else {
        throw new Error(`Tool '${toolName}' does not have an execute method`);
      }

      return {
        toolCallId: toolCall.id,
        result: this.formatToolResult(result),
        success: true,
        executionTime: Date.now() - startTime
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      return {
        toolCallId: toolCall.id,
        result: `Error executing tool '${toolName}': ${errorMessage}`,
        success: false,
        error: errorMessage,
        executionTime: Date.now() - startTime
      };
    }
  }

  private async executeShellCommand(args: any): Promise<any> {
    const { command, workingDirectory, timeout, requireApproval } = args;

    if (!command) {
      throw new CommandError('Command is required');
    }

    // Execute the shell command
    const result = await this.shellTool.execute(command, {
      workingDirectory,
      timeout,
      requireApproval
    });

    return result;
  }

  private formatToolResult(result: any): string {
    if (typeof result === 'string') {
      return result;
    }

    if (result && typeof result === 'object') {
      // Handle shell command results
      if ('stdout' in result && 'stderr' in result && 'exitCode' in result) {
        const output = [];
        
        if (result.stdout) {
          output.push(`STDOUT:\n${result.stdout}`);
        }
        
        if (result.stderr) {
          output.push(`STDERR:\n${result.stderr}`);
        }
        
        output.push(`Exit Code: ${result.exitCode}`);
        output.push(`Execution Time: ${result.executionTime}ms`);
        output.push(`Success: ${result.success}`);
        
        return output.join('\n\n');
      }

      // For other object types, stringify them
      try {
        return JSON.stringify(result, null, 2);
      } catch {
        return String(result);
      }
    }

    return String(result);
  }

  public async executeToolCalls(toolCalls: ToolCall[]): Promise<ToolResult[]> {
    const results: ToolResult[] = [];
    
    // Execute tool calls sequentially to maintain order and handle dependencies
    for (const toolCall of toolCalls) {
      const result = await this.executeToolCall(toolCall);
      results.push(result);
    }

    return results;
  }

  public createToolResultMessages(toolResults: ToolResult[]): LLMMessage[] {
    return toolResults.map(result => ({
      id: nanoid(),
      role: 'tool' as const,
      content: result.result,
      toolCallId: result.toolCallId,
      timestamp: new Date()
    }));
  }

  public setWorkingDirectory(directory: string): void {
    this.shellTool.setWorkingDirectory(directory);
  }

  public getWorkingDirectory(): string {
    return this.shellTool.getWorkingDirectory();
  }

  public async validateWorkingDirectory(directory: string): Promise<boolean> {
    return await this.shellTool.validateDirectory(directory);
  }

  public async getCurrentDirectory(): Promise<string> {
    return await this.shellTool.getCurrentDirectory();
  }

  public getAvailableTools(): string[] {
    return Array.from(this.tools.keys());
  }

  public hasToolCalls(message: LLMMessage): boolean {
    return !!(message.toolCalls && message.toolCalls.length > 0);
  }

  public extractToolCalls(message: LLMMessage): ToolCall[] {
    return message.toolCalls || [];
  }

  public async processMessageWithTools(
    message: LLMMessage,
    onToolExecution?: (toolCall: ToolCall, result: ToolResult) => void
  ): Promise<LLMMessage[]> {
    const resultMessages: LLMMessage[] = [];

    if (this.hasToolCalls(message)) {
      const toolCalls = this.extractToolCalls(message);
      
      for (const toolCall of toolCalls) {
        const result = await this.executeToolCall(toolCall);
        
        // Call the callback if provided
        if (onToolExecution) {
          onToolExecution(toolCall, result);
        }

        // Create tool result message
        const toolResultMessage: LLMMessage = {
          id: nanoid(),
          role: 'tool',
          content: result.result,
          toolCallId: result.toolCallId,
          timestamp: new Date()
        };

        resultMessages.push(toolResultMessage);
      }
    }

    return resultMessages;
  }

  public getToolUsageStats(): {
    totalExecutions: number;
    toolUsage: Record<string, number>;
    averageExecutionTime: number;
  } {
    // This would be implemented with proper tracking in a real application
    return {
      totalExecutions: 0,
      toolUsage: {},
      averageExecutionTime: 0
    };
  }
}
