#!/usr/bin/env node

import { Command } from 'commander';
import chalk from 'chalk';
import { Terminal } from './components/terminal.js';
import { OnboardingFlow } from './components/onboarding.js';
import { ConfigManager } from './core/config.js';
import { SessionManager } from './core/session-manager.js';
import { LLMManager } from './core/llm-manager.js';
import { ModernSpinner } from './components/spinner.js';
// Import version from package.json
import { readFileSync } from 'fs';
import { join } from 'path';
const packageJson = JSON.parse(readFileSync(join(process.cwd(), 'package.json'), 'utf-8'));
const version = packageJson.version;

const program = new Command();

// Global error handling
process.on('uncaughtException', (error) => {
  console.error(chalk.red('Uncaught Exception:'), error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('Unhandled Rejection at:'), promise, chalk.red('reason:'), reason);
  process.exit(1);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log(chalk.yellow('\n\nGracefully shutting down...'));
  ModernSpinner.stopAll();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log(chalk.yellow('\n\nReceived SIGTERM, shutting down...'));
  ModernSpinner.stopAll();
  process.exit(0);
});

// CLI Configuration
program
  .name('arien')
  .description('Modern and powerful CLI terminal system with LLM integration and function calling capabilities')
  .version(version);

// Main interactive command
program
  .command('start')
  .description('Start the interactive terminal')
  .option('-q, --quick', 'Quick start with minimal configuration')
  .option('-r, --reconfigure', 'Force reconfiguration')
  .action(async (options) => {
    try {
      // Stop any running spinners before starting
      ModernSpinner.stopAll();

      const configManager = ConfigManager.getInstance();

      if (options.reconfigure) {
        // Force reconfiguration
        const onboarding = new OnboardingFlow();
        const result = await onboarding.start();

        if (result.autoStart === false) {
          return;
        }

        if (result.success) {
          const terminal = new Terminal();
          await terminal.start();
        }
      } else if (options.quick) {
        // Quick start
        const terminal = new Terminal();
        await terminal.quickStart();
      } else {
        // Normal start - check if configured
        if (!configManager.isConfigured()) {
          // Run onboarding first
          const onboarding = new OnboardingFlow();
          const result = await onboarding.start();

          if (result.autoStart === false) {
            return;
          }

          if (result.success) {
            const terminal = new Terminal();
            await terminal.start();
          }
        } else {
          // Already configured, start terminal directly
          const terminal = new Terminal();
          await terminal.start();
        }
      }

    } catch (error) {
      console.error(chalk.red('Failed to start terminal:'), error);
      process.exit(1);
    }
  });

// Configuration commands
program
  .command('config')
  .description('Manage configuration')
  .option('-s, --show', 'Show current configuration')
  .option('-r, --reset', 'Reset configuration to defaults')
  .option('-p, --path', 'Show configuration file path')
  .option('-q, --quick-setup', 'Quick setup with minimal prompts')
  .action(async (options) => {
    const configManager = ConfigManager.getInstance();

    try {
      if (options.show) {
        const config = configManager.getAll();
        console.log(chalk.blue('Current Configuration:'));
        Object.entries(config).forEach(([key, value]) => {
          const displayValue = key === 'apiKey' && value ? '***hidden***' : value;
          console.log(chalk.gray(`  ${key}: ${displayValue}`));
        });
      } else if (options.reset) {
        configManager.reset();
        console.log(chalk.green('Configuration reset successfully.'));
      } else if (options.path) {
        console.log(chalk.blue('Configuration file path:'));
        console.log(configManager.getConfigPath());
      } else if (options.quickSetup) {
        // Quick setup bypass
        const onboarding = new OnboardingFlow();
        const result = await onboarding.quickSetup();

        if (result.success) {
          console.log(chalk.green('Quick setup completed successfully!'));
          const terminal = new Terminal();
          await terminal.start();
        } else {
          console.error(chalk.red('Quick setup failed:'), result.message);
        }
      } else {
        // Stop any running spinners before starting onboarding
        ModernSpinner.stopAll();

        // Run onboarding
        const onboarding = new OnboardingFlow();
        const result = await onboarding.start();

        // If user chose to auto-start, continue to terminal
        if (result.autoStart === false) {
          return; // Exit without starting terminal
        }

        // Start terminal after successful onboarding
        if (result.success) {
          const terminal = new Terminal();
          await terminal.start();
        }
      }
    } catch (error) {
      console.error(chalk.red('Configuration error:'), error);
      process.exit(1);
    }
  });

// Session management commands
program
  .command('session')
  .description('Manage chat sessions')
  .option('-l, --list', 'List all sessions')
  .option('-d, --delete <id>', 'Delete a session')
  .option('-e, --export <id>', 'Export a session')
  .option('-i, --import <file>', 'Import a session from file')
  .option('-c, --cleanup', 'Clean up old sessions')
  .action(async (options) => {
    const sessionManager = new SessionManager();
    
    try {
      if (options.list) {
        const sessions = sessionManager.getAllSessions();
        console.log(chalk.blue(`Found ${sessions.length} sessions:`));
        sessions.forEach(session => {
          const stats = sessionManager.getSessionStats(session.id);
          console.log(chalk.white(`  ${session.name} (${session.id.slice(0, 8)})`));
          console.log(chalk.gray(`    Messages: ${stats.messageCount}, Created: ${session.createdAt.toLocaleString()}`));
        });
      } else if (options.delete) {
        const success = sessionManager.deleteSession(options.delete);
        if (success) {
          console.log(chalk.green(`Session ${options.delete} deleted successfully.`));
        } else {
          console.log(chalk.red(`Session ${options.delete} not found.`));
        }
      } else if (options.export) {
        const exportData = sessionManager.exportSession(options.export);
        console.log(exportData);
      } else if (options.import) {
        const fs = await import('fs');
        const data = fs.readFileSync(options.import, 'utf8');
        const session = sessionManager.importSession(data);
        console.log(chalk.green(`Session imported: ${session.name} (${session.id})`));
      } else if (options.cleanup) {
        const deletedCount = sessionManager.cleanupOldSessions();
        console.log(chalk.green(`Cleaned up ${deletedCount} old sessions.`));
      } else {
        console.log(chalk.yellow('Please specify an action. Use --help for options.'));
      }
    } catch (error) {
      console.error(chalk.red('Session management error:'), error);
      process.exit(1);
    }
  });

// Provider management commands
program
  .command('provider')
  .description('Manage AI providers')
  .option('-l, --list', 'List available providers')
  .option('-s, --switch <provider>', 'Switch to a provider (deepseek|ollama)')
  .option('-m, --models [provider]', 'List available models for provider')
  .option('-t, --test', 'Test current provider connection')
  .action(async (options) => {
    const llmManager = new LLMManager();
    const configManager = ConfigManager.getInstance();
    
    try {
      if (options.list) {
        console.log(chalk.blue('Available providers:'));
        console.log(chalk.white('  deepseek - Cloud-based AI with advanced models'));
        console.log(chalk.white('  ollama   - Local AI models'));
        
        const currentProvider = configManager.get('provider');
        console.log(chalk.green(`\nCurrent provider: ${currentProvider}`));
        
      } else if (options.switch) {
        if (!['deepseek', 'ollama'].includes(options.switch)) {
          console.log(chalk.red('Invalid provider. Use: deepseek or ollama'));
          process.exit(1);
        }
        
        await llmManager.switchProvider(options.switch);
        console.log(chalk.green(`Switched to provider: ${options.switch}`));
        
      } else if (options.models) {
        const provider = options.models || configManager.get('provider');
        console.log(chalk.blue(`Available models for ${provider}:`));
        
        const models = await llmManager.getAvailableModels();
        const currentModel = configManager.get('model');
        
        models.forEach(model => {
          const indicator = model === currentModel ? chalk.green('● ') : '  ';
          console.log(`${indicator}${model}`);
        });
        
      } else if (options.test) {
        const spinner = ModernSpinner.createConnectingSpinner('Testing provider connection...');
        spinner.start();
        
        try {
          await llmManager.initializeProvider();
          const isConnected = await llmManager.validateConnection();
          
          if (isConnected) {
            spinner.succeed('Provider connection successful!');
          } else {
            spinner.fail('Provider connection failed!');
            process.exit(1);
          }
        } catch (error) {
          spinner.fail('Provider connection failed!');
          console.error(chalk.red('Error:'), error);
          process.exit(1);
        }
        
      } else {
        console.log(chalk.yellow('Please specify an action. Use --help for options.'));
      }
    } catch (error) {
      console.error(chalk.red('Provider management error:'), error);
      process.exit(1);
    }
  });

// Execute single command
program
  .command('exec <command>')
  .description('Execute a single command and exit')
  .option('-s, --session <id>', 'Use specific session')
  .action(async (command, options) => {
    try {
      const configManager = ConfigManager.getInstance();
      
      if (!configManager.isConfigured()) {
        console.log(chalk.red('Not configured. Run "arien config" first.'));
        process.exit(1);
      }
      
      const terminal = new Terminal();
      await terminal.quickStart();
      
      if (options.session) {
        terminal.getEngine().switchSession(options.session);
      }
      
      await terminal.executeCommand(command);
      
      // Wait a bit for async operations to complete
      setTimeout(() => {
        terminal.destroy();
        process.exit(0);
      }, 1000);
      
    } catch (error) {
      console.error(chalk.red('Execution error:'), error);
      process.exit(1);
    }
  });

// Version command (already handled by commander)
program
  .command('version')
  .description('Show version information')
  .action(() => {
    console.log(chalk.blue.bold('Arien AI CLI'));
    console.log(chalk.white(`Version: ${version}`));
    console.log(chalk.gray('A modern and powerful CLI terminal system with LLM integration'));
  });

// Help command enhancement
program.on('--help', () => {
  console.log('');
  console.log(chalk.blue.bold('Examples:'));
  console.log(chalk.white('  arien                    # Start interactive terminal'));
  console.log(chalk.white('  arien --quick            # Quick start with minimal setup'));
  console.log(chalk.white('  arien config             # Configure providers and settings'));
  console.log(chalk.white('  arien session --list     # List all chat sessions'));
  console.log(chalk.white('  arien provider --test    # Test provider connection'));
  console.log(chalk.white('  arien exec "ls -la"      # Execute single command'));
  console.log('');
  console.log(chalk.gray('For more information, visit: https://github.com/arien-ai/arien-ai-cli'));
});

// If no command provided, default to start
if (!process.argv.slice(2).length) {
  process.argv.push('start');
}

// Parse command line arguments
program.parse();
