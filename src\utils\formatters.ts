import chalk from 'chalk';
import { format } from 'date-fns';
import stripAnsi from 'strip-ansi';

export interface FormatOptions {
  maxWidth?: number;
  indent?: number;
  showLineNumbers?: boolean;
  highlightSyntax?: boolean;
  wrapText?: boolean;
}

export class OutputFormatters {
  /**
   * Format command output with syntax highlighting
   */
  public static formatCommandOutput(
    output: string,
    command: string,
    options: FormatOptions = {}
  ): string {
    const opts = {
      maxWidth: 120,
      indent: 2,
      showLineNumbers: false,
      highlightSyntax: true,
      wrapText: true,
      ...options
    };

    const lines = output.split('\n');
    const formattedLines = lines.map((line, index) => {
      let formattedLine = line;

      // Apply syntax highlighting based on content
      if (opts.highlightSyntax) {
        formattedLine = this.applySyntaxHighlighting(formattedLine, command);
      }

      // Add line numbers if requested
      if (opts.showLineNumbers) {
        const lineNumber = chalk.gray(`${(index + 1).toString().padStart(3)}: `);
        formattedLine = lineNumber + formattedLine;
      }

      // Apply indentation
      if (opts.indent > 0) {
        formattedLine = ' '.repeat(opts.indent) + formattedLine;
      }

      // Wrap text if needed
      if (opts.wrapText && stripAnsi(formattedLine).length > opts.maxWidth) {
        formattedLine = this.wrapText(formattedLine, opts.maxWidth, opts.indent);
      }

      return formattedLine;
    });

    return formattedLines.join('\n');
  }

  /**
   * Apply syntax highlighting based on command type and content
   */
  private static applySyntaxHighlighting(line: string, command: string): string {
    // File listing (ls, dir)
    if (/^(ls|dir|ll)/.test(command)) {
      return this.highlightFileList(line);
    }

    // Git output
    if (/^git/.test(command)) {
      return this.highlightGitOutput(line);
    }

    // Process listing (ps, top)
    if (/^(ps|top|htop)/.test(command)) {
      return this.highlightProcessList(line);
    }

    // Network output (ping, curl, wget)
    if (/^(ping|curl|wget|netstat)/.test(command)) {
      return this.highlightNetworkOutput(line);
    }

    // Log files
    if (/\.(log|out|err)$/.test(command) || /tail|head|cat.*\.log/.test(command)) {
      return this.highlightLogOutput(line);
    }

    // JSON output
    if (this.isJsonLine(line)) {
      return this.highlightJson(line);
    }

    // Error patterns
    if (/error|fail|exception/i.test(line)) {
      return chalk.red(line);
    }

    // Warning patterns
    if (/warn|warning|caution/i.test(line)) {
      return chalk.yellow(line);
    }

    // Success patterns
    if (/success|complete|done|ok/i.test(line)) {
      return chalk.green(line);
    }

    return line;
  }

  /**
   * Highlight file listing output
   */
  private static highlightFileList(line: string): string {
    // Directory entries
    if (/^d/.test(line)) {
      return chalk.blue(line);
    }

    // Executable files
    if (/^-.*x/.test(line)) {
      return chalk.green(line);
    }

    // Hidden files
    if (/\s\.[^.\s]/.test(line)) {
      return chalk.gray(line);
    }

    // File extensions
    line = line.replace(/\.([a-zA-Z0-9]+)(\s|$)/g, (match, ext, space) => {
      const colors = {
        js: chalk.yellow,
        ts: chalk.blue,
        py: chalk.green,
        java: chalk.red,
        cpp: chalk.magenta,
        c: chalk.magenta,
        html: chalk.red,
        css: chalk.blue,
        json: chalk.yellow,
        xml: chalk.cyan,
        md: chalk.white,
        txt: chalk.gray
      };

      const colorFn = colors[ext.toLowerCase() as keyof typeof colors] || chalk.white;
      return colorFn(`.${ext}`) + space;
    });

    return line;
  }

  /**
   * Highlight Git output
   */
  private static highlightGitOutput(line: string): string {
    // Git status
    if (/^\s*modified:/.test(line)) {
      return chalk.yellow(line);
    }
    if (/^\s*deleted:/.test(line)) {
      return chalk.red(line);
    }
    if (/^\s*new file:/.test(line)) {
      return chalk.green(line);
    }
    if (/^\s*renamed:/.test(line)) {
      return chalk.blue(line);
    }

    // Git log
    if (/^commit\s+[a-f0-9]+/.test(line)) {
      return chalk.yellow(line);
    }
    if (/^Author:/.test(line)) {
      return chalk.cyan(line);
    }
    if (/^Date:/.test(line)) {
      return chalk.gray(line);
    }

    // Git diff
    if (/^\+/.test(line)) {
      return chalk.green(line);
    }
    if (/^-/.test(line)) {
      return chalk.red(line);
    }
    if (/^@@/.test(line)) {
      return chalk.cyan(line);
    }

    return line;
  }

  /**
   * Highlight process listing
   */
  private static highlightProcessList(line: string): string {
    // Header line
    if (/PID|USER|CPU|MEM|COMMAND/.test(line)) {
      return chalk.bold.white(line);
    }

    // High CPU usage
    if (/\s+([5-9]\d|\d{3,})\.\d+\s+/.test(line)) {
      return chalk.red(line);
    }

    // Medium CPU usage
    if (/\s+[2-4]\d\.\d+\s+/.test(line)) {
      return chalk.yellow(line);
    }

    return line;
  }

  /**
   * Highlight network output
   */
  private static highlightNetworkOutput(line: string): string {
    // IP addresses
    line = line.replace(/\b(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\b/g, chalk.cyan('$1'));

    // Ports
    line = line.replace(/:(\d+)\b/g, ':' + chalk.yellow('$1'));

    // HTTP status codes
    line = line.replace(/\b(2\d{2})\b/g, chalk.green('$1')); // 2xx success
    line = line.replace(/\b(3\d{2})\b/g, chalk.yellow('$1')); // 3xx redirect
    line = line.replace(/\b(4\d{2})\b/g, chalk.red('$1')); // 4xx client error
    line = line.replace(/\b(5\d{2})\b/g, chalk.red.bold('$1')); // 5xx server error

    // Ping times
    line = line.replace(/time=(\d+(?:\.\d+)?)\s*ms/g, (match, time) => {
      const timeNum = parseFloat(time);
      if (timeNum < 50) return `time=${chalk.green(time)}ms`;
      if (timeNum < 200) return `time=${chalk.yellow(time)}ms`;
      return `time=${chalk.red(time)}ms`;
    });

    return line;
  }

  /**
   * Highlight log output
   */
  private static highlightLogOutput(line: string): string {
    // Timestamp
    line = line.replace(/^\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}/, chalk.gray('$&'));

    // Log levels
    line = line.replace(/\b(ERROR|FATAL)\b/gi, chalk.red.bold('$1'));
    line = line.replace(/\b(WARN|WARNING)\b/gi, chalk.yellow('$1'));
    line = line.replace(/\b(INFO|INFORMATION)\b/gi, chalk.blue('$1'));
    line = line.replace(/\b(DEBUG|TRACE)\b/gi, chalk.gray('$1'));

    return line;
  }

  /**
   * Check if line contains JSON
   */
  private static isJsonLine(line: string): boolean {
    const trimmed = line.trim();
    return (trimmed.startsWith('{') && trimmed.endsWith('}')) ||
           (trimmed.startsWith('[') && trimmed.endsWith(']'));
  }

  /**
   * Highlight JSON content
   */
  private static highlightJson(line: string): string {
    try {
      const parsed = JSON.parse(line);
      return this.formatJsonObject(parsed, 0);
    } catch {
      // If not valid JSON, apply basic highlighting
      line = line.replace(/"([^"]+)":/g, chalk.cyan('"$1"') + ':');
      line = line.replace(/:\s*"([^"]+)"/g, ': ' + chalk.green('"$1"'));
      line = line.replace(/:\s*(true|false|null)/g, ': ' + chalk.yellow('$1'));
      line = line.replace(/:\s*(\d+(?:\.\d+)?)/g, ': ' + chalk.magenta('$1'));
      return line;
    }
  }

  /**
   * Format JSON object with colors
   */
  private static formatJsonObject(obj: any, indent: number): string {
    const spaces = '  '.repeat(indent);
    
    if (typeof obj === 'string') {
      return chalk.green(`"${obj}"`);
    } else if (typeof obj === 'number') {
      return chalk.magenta(obj.toString());
    } else if (typeof obj === 'boolean' || obj === null) {
      return chalk.yellow(String(obj));
    } else if (Array.isArray(obj)) {
      if (obj.length === 0) return '[]';
      const items = obj.map(item => 
        spaces + '  ' + this.formatJsonObject(item, indent + 1)
      ).join(',\n');
      return `[\n${items}\n${spaces}]`;
    } else if (typeof obj === 'object') {
      const entries = Object.entries(obj);
      if (entries.length === 0) return '{}';
      const items = entries.map(([key, value]) => 
        spaces + '  ' + chalk.cyan(`"${key}"`) + ': ' + this.formatJsonObject(value, indent + 1)
      ).join(',\n');
      return `{\n${items}\n${spaces}}`;
    }
    
    return String(obj);
  }

  /**
   * Wrap text to specified width
   */
  private static wrapText(text: string, maxWidth: number, indent: number = 0): string {
    const plainText = stripAnsi(text);
    if (plainText.length <= maxWidth) return text;

    const words = text.split(' ');
    const lines: string[] = [];
    let currentLine = '';

    for (const word of words) {
      const plainWord = stripAnsi(word);
      const plainCurrentLine = stripAnsi(currentLine);

      if (plainCurrentLine.length + plainWord.length + 1 <= maxWidth) {
        currentLine += (currentLine ? ' ' : '') + word;
      } else {
        if (currentLine) lines.push(currentLine);
        currentLine = ' '.repeat(indent) + word;
      }
    }

    if (currentLine) lines.push(currentLine);
    return lines.join('\n');
  }

  /**
   * Format table data
   */
  public static formatTable(
    data: string[][],
    headers?: string[],
    options: { 
      border?: boolean;
      padding?: number;
      alignment?: ('left' | 'center' | 'right')[];
    } = {}
  ): string {
    const opts = {
      border: true,
      padding: 1,
      alignment: [],
      ...options
    };

    if (data.length === 0) return '';

    // Calculate column widths
    const allRows = headers ? [headers, ...data] : data;
    const colWidths = allRows[0].map((_, colIndex) => 
      Math.max(...allRows.map(row => stripAnsi(row[colIndex] || '').length))
    );

    // Format rows
    const formatRow = (row: string[], isHeader: boolean = false) => {
      const cells = row.map((cell, index) => {
        const width = colWidths[index];
        const padding = ' '.repeat(opts.padding);
        const alignment = opts.alignment[index] || 'left';
        
        let formattedCell = cell;
        const plainCell = stripAnsi(cell);
        
        if (alignment === 'center') {
          const spaces = width - plainCell.length;
          const leftSpaces = Math.floor(spaces / 2);
          const rightSpaces = spaces - leftSpaces;
          formattedCell = ' '.repeat(leftSpaces) + cell + ' '.repeat(rightSpaces);
        } else if (alignment === 'right') {
          formattedCell = ' '.repeat(width - plainCell.length) + cell;
        } else {
          formattedCell = cell + ' '.repeat(width - plainCell.length);
        }

        if (isHeader) {
          formattedCell = chalk.bold.white(formattedCell);
        }

        return padding + formattedCell + padding;
      });

      const content = cells.join(opts.border ? '│' : ' ');
      return opts.border ? `│${content}│` : content;
    };

    const lines: string[] = [];

    // Top border
    if (opts.border) {
      const borderLine = '┌' + colWidths.map(width => 
        '─'.repeat(width + opts.padding * 2)
      ).join('┬') + '┐';
      lines.push(borderLine);
    }

    // Header
    if (headers) {
      lines.push(formatRow(headers, true));
      
      if (opts.border) {
        const separatorLine = '├' + colWidths.map(width => 
          '─'.repeat(width + opts.padding * 2)
        ).join('┼') + '┤';
        lines.push(separatorLine);
      }
    }

    // Data rows
    data.forEach(row => {
      lines.push(formatRow(row));
    });

    // Bottom border
    if (opts.border) {
      const borderLine = '└' + colWidths.map(width => 
        '─'.repeat(width + opts.padding * 2)
      ).join('┴') + '┘';
      lines.push(borderLine);
    }

    return lines.join('\n');
  }

  /**
   * Format progress bar
   */
  public static formatProgressBar(
    current: number,
    total: number,
    width: number = 40,
    options: {
      showPercentage?: boolean;
      showNumbers?: boolean;
      style?: 'blocks' | 'bars' | 'dots';
    } = {}
  ): string {
    const opts = {
      showPercentage: true,
      showNumbers: false,
      style: 'blocks' as const,
      ...options
    };

    const percentage = Math.min(100, Math.max(0, (current / total) * 100));
    const filled = Math.floor((percentage / 100) * width);
    const empty = width - filled;

    let bar = '';
    switch (opts.style) {
      case 'blocks':
        bar = chalk.green('█'.repeat(filled)) + chalk.gray('░'.repeat(empty));
        break;
      case 'bars':
        bar = chalk.green('|'.repeat(filled)) + chalk.gray('-'.repeat(empty));
        break;
      case 'dots':
        bar = chalk.green('●'.repeat(filled)) + chalk.gray('○'.repeat(empty));
        break;
    }

    let result = `[${bar}]`;

    if (opts.showPercentage) {
      result += ` ${percentage.toFixed(1)}%`;
    }

    if (opts.showNumbers) {
      result += ` (${current}/${total})`;
    }

    return result;
  }

  /**
   * Format file size
   */
  public static formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    const formatted = unitIndex === 0 ? size.toString() : size.toFixed(1);
    return `${formatted} ${units[unitIndex]}`;
  }

  /**
   * Format duration
   */
  public static formatDuration(milliseconds: number): string {
    if (milliseconds < 1000) {
      return `${milliseconds}ms`;
    }

    const seconds = Math.floor(milliseconds / 1000);
    if (seconds < 60) {
      return `${seconds}s`;
    }

    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    if (minutes < 60) {
      return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
    }

    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  }

  /**
   * Format timestamp
   */
  public static formatTimestamp(date: Date, style: 'short' | 'medium' | 'long' = 'medium'): string {
    switch (style) {
      case 'short':
        return format(date, 'HH:mm:ss');
      case 'medium':
        return format(date, 'MMM dd HH:mm:ss');
      case 'long':
        return format(date, 'yyyy-MM-dd HH:mm:ss');
      default:
        return date.toISOString();
    }
  }
}
