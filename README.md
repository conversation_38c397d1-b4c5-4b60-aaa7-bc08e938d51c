# Arien AI CLI

A modern and powerful CLI terminal system with LLM integration and function calling capabilities. Built with TypeScript 5.8.3 and Node.js 20+.

## 🚀 Features

### Core Capabilities
- **LLM Integration**: Support for Deepseek and Ollama providers
- **Function Calling**: Execute shell commands through natural language
- **Session Management**: Persistent chat sessions with history
- **Modern Terminal UI**: Responsive layout with real-time updates
- **Cross-Platform**: Windows WSL, macOS, and Linux support

### LLM Providers
- **Deepseek**: Cloud-based AI with models like `deepseek-chat` and `deepseek-reasoner`
- **Ollama**: Local AI models running on your machine

### Terminal Interface Components
- **Terminal Responsive Layout**: Adaptive UI that works across different screen sizes
- **Onboarding Flow**: Guided setup with auto-start functionality after configuration
- **Main CLI Terminal**: Interactive chat interface with real-time updates
- **Enhanced Chat Input Processing**: Auto-completion, history, and validation
- **Advanced Chat Output Processing**: Syntax highlighting, formatting, and structured display
- **Dynamic Header Component**: Shows session info, provider status, and working directory
- **Message History Management**: Searchable, exportable history with date grouping
- **Modern Spinner Animations**: Ball spinners, wave animations, and progress indicators
- **Comprehensive Slash Commands**: `/model`, `/provider`, `/session`, `/history`, `/config`, `/help`, `/status`, `/cd`
- **Tool Calls Processing**: Parallel/sequential execution with detailed progress tracking

### 🚀 Enhanced Features

#### Intelligent Error Handling & Retry Logic
- **Exponential Backoff**: Automatic retry with intelligent delays for network issues
- **Rate Limit Handling**: Smart handling of API rate limits with appropriate wait times
- **Error Categorization**: Automatic classification of errors (network, provider, command, config, system, user)
- **Suggested Actions**: Context-aware suggestions for resolving errors
- **Comprehensive Logging**: Detailed error tracking with severity levels and history

#### Advanced Tool System
- **Detailed Tool Descriptions**: Comprehensive documentation for when and how to use each tool
- **Parallel Execution Support**: Execute independent operations simultaneously for better performance
- **Sequential Execution**: Ensure dependent operations run in correct order
- **Safety Validation**: Prevent dangerous operations with built-in safety checks
- **Cross-Platform Compatibility**: Seamless operation across Windows, macOS, and Linux

#### Enhanced System Prompts
- **Context-Aware Prompts**: Dynamic prompts based on user context and history
- **Detailed Guidelines**: Comprehensive instructions for AI behavior and safety
- **Tool Usage Examples**: Extensive examples showing proper tool usage patterns
- **Safety Instructions**: Built-in safety protocols and best practices
- **Scenario-Specific Prompts**: Specialized prompts for development, sysadmin, and data processing

#### Modern UI/UX Components
- **Responsive Terminal Layout**: Adaptive interface that works on any screen size
- **Real-Time Updates**: Live feedback during command execution and AI processing
- **Syntax Highlighting**: Color-coded output for different command types and content
- **Progress Indicators**: Visual feedback for long-running operations
- **Interactive Elements**: Mouse support, keyboard shortcuts, and focus management

#### Session & History Management
- **Persistent Sessions**: Save and restore conversation contexts across restarts
- **Message Search**: Full-text search across all messages and sessions
- **Export/Import**: Backup and restore sessions in multiple formats
- **Statistics**: Detailed analytics on usage patterns and performance
- **Date Grouping**: Organize messages by date for better navigation

## 📦 Installation

### Quick Install (Recommended)
```bash
curl -fsSL https://raw.githubusercontent.com/arien-ai/arien-ai-cli/main/install.sh | bash
```

### Manual Installation
```bash
# Clone the repository
git clone https://github.com/arien-ai/arien-ai-cli.git
cd arien-ai-cli

# Install dependencies
npm install

# Build the project
npm run build

# Install globally
npm link

# Or install via npm (when published)
npm install -g arien-ai-cli
```

### Platform-Specific Installation

#### Windows WSL
```bash
./install.sh install
```

#### macOS
```bash
./install.sh install
```

#### Linux
```bash
./install.sh install
```

## 🛠️ Configuration

### First Time Setup
Run the onboarding flow to configure your AI provider:

```bash
arien config
```

### Quick Setup
For development or testing with minimal configuration:

```bash
arien --quick
```

### Manual Configuration
```bash
# Show current configuration
arien config --show

# Reset configuration
arien config --reset

# Show config file path
arien config --path
```

## 🎯 Usage

### Interactive Mode (Default)
```bash
arien
# or
arien start
```

### Slash Commands
- `/help` - Show available commands
- `/model [name]` - Switch AI model or list available models
- `/provider [name]` - Switch AI provider or show current
- `/session [action]` - Manage chat sessions
  - `/session new [name]` - Create new session
  - `/session list` - List all sessions
  - `/session switch <id>` - Switch to session
  - `/session delete <id>` - Delete session
  - `/session export <id>` - Export session
  - `/session clear` - Clear current session
- `/history [limit]` - Show message history
- `/config [key] [value]` - View or modify configuration
- `/clear` - Clear terminal screen
- `/status` - Show system status
- `/cd [path]` - Change working directory

### Command Line Interface
```bash
# Execute single command
arien exec "list all files in current directory"

# Use specific session
arien exec "git status" --session <session-id>

# Session management
arien session --list
arien session --delete <session-id>
arien session --export <session-id>

# Provider management
arien provider --list
arien provider --switch deepseek
arien provider --models
arien provider --test

# Show version
arien version
```

## 🔧 Function Calling

The CLI includes a powerful shell tool that can execute system commands through natural language:

### Shell Tool Features
- **Safe Command Execution**: Validates commands before execution
- **Cross-Platform Support**: Works on Windows, macOS, and Linux
- **Error Handling**: Comprehensive error reporting and recovery
- **Timeout Management**: Prevents hanging commands
- **Working Directory Management**: Maintains context across commands

### Usage Examples
```
# Natural language commands
"list all files in the current directory"
"create a new folder called 'project'"
"check git status"
"install npm dependencies"
"run the build script"

# Direct shell commands
"ls -la"
"mkdir project"
"git status"
"npm install"
"npm run build"
```

### Tool Usage Guidelines
- **When to Use**: File operations, system info, development tasks, network operations
- **When Not to Use**: Destructive operations without approval, interactive commands
- **Parallel Execution**: Independent operations (multiple file checks, parallel builds)
- **Sequential Execution**: Dependent operations (cd then ls, compile then run)

## 🎨 Terminal Interface

### Layout Components
- **Header**: Shows current session, provider, model, and working directory
- **Chat Area**: Main conversation interface with syntax highlighting
- **Input Area**: Command input with auto-completion
- **Status Bar**: Real-time status updates and operation feedback
- **Sidebar**: Session management (toggle with Ctrl+S)

### Keyboard Shortcuts
- `Tab` / `Shift+Tab` - Navigate between interface elements
- `Ctrl+S` - Toggle sidebar
- `Ctrl+C` / `Escape` / `q` - Exit application
- `Up/Down` - Scroll chat history
- `Page Up/Down` - Fast scroll chat history

## 🔄 Session Management

### Features
- **Persistent Sessions**: Chat history saved automatically
- **Multiple Sessions**: Switch between different conversation contexts
- **Export/Import**: Share sessions or backup conversations
- **Session Statistics**: Track message counts and activity
- **Auto-cleanup**: Remove old sessions automatically

### Session Commands
```bash
# Create new session
arien session new "Project Planning"

# List all sessions
arien session --list

# Switch to session
arien session switch <session-id>

# Export session
arien session --export <session-id> > session.json

# Import session
arien session --import session.json

# Clean up old sessions
arien session --cleanup
```

## ⚙️ Configuration

### Provider Configuration

#### Deepseek Setup
```bash
# Configure Deepseek
arien config

# Manual configuration
export DEEPSEEK_API_KEY="your-api-key"
arien provider --switch deepseek
```

#### Ollama Setup
```bash
# Install Ollama first
curl -fsSL https://ollama.ai/install.sh | sh

# Pull a model
ollama pull llama2

# Configure Arien AI CLI
arien provider --switch ollama
```

### Environment Variables
```bash
# Deepseek
export DEEPSEEK_API_KEY="your-api-key"
export DEEPSEEK_BASE_URL="https://api.deepseek.com/v1"

# Ollama
export OLLAMA_BASE_URL="http://localhost:11434"

# General
export ARIEN_WORKING_DIR="/path/to/working/directory"
export ARIEN_AUTO_APPROVE="false"
```

## 🧪 Development

### Prerequisites
- Node.js 20+
- npm or yarn
- TypeScript 5.8.3

### Setup
```bash
# Clone repository
git clone https://github.com/arien-ai/arien-ai-cli.git
cd arien-ai-cli

# Install dependencies
npm install

# Development mode
npm run dev

# Build
npm run build

# Run tests
npm test

# Lint and format
npm run lint
npm run format
```

### Project Structure
```
src/
├── components/          # UI components
│   ├── terminal-layout.ts
│   ├── onboarding.ts
│   ├── slash-commands.ts
│   ├── spinner.ts
│   └── terminal.ts
├── core/               # Core system components
│   ├── config.ts
│   ├── llm-manager.ts
│   ├── session-manager.ts
│   ├── terminal-engine.ts
│   └── tool-manager.ts
├── providers/          # LLM provider implementations
│   ├── deepseek.ts
│   └── ollama.ts
├── tools/             # Function calling tools
│   └── shell.ts
├── types/             # TypeScript type definitions
│   └── index.ts
├── utils/             # Utility functions
└── index.ts           # Main entry point
```

## 🔒 Security

### Safety Features
- **Command Validation**: Prevents execution of dangerous commands
- **User Approval**: Prompts for confirmation on destructive operations
- **Sandboxed Execution**: Commands run in controlled environment
- **API Key Protection**: Secure storage and handling of credentials
- **Error Isolation**: Prevents system crashes from command failures

### Best Practices
- Always review commands before execution
- Use session isolation for different projects
- Regularly backup important sessions
- Keep API keys secure and rotate them regularly
- Monitor command execution logs

## 📚 Examples

### Basic Usage
```bash
# Start interactive terminal
arien

# Ask AI to help with file management
> "show me all TypeScript files in this project"

# Execute git operations
> "check git status and show recent commits"

# Development tasks
> "install dependencies and run the build"
```

### Advanced Usage
```bash
# Complex system operations
> "find all large files over 100MB and show their sizes"

# Multi-step development workflow
> "create a new React component, add it to the index, and run tests"

# System monitoring
> "check system resources and running processes"
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Run linting and tests
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [GitHub Wiki](https://github.com/arien-ai/arien-ai-cli/wiki)
- **Issues**: [GitHub Issues](https://github.com/arien-ai/arien-ai-cli/issues)
- **Discussions**: [GitHub Discussions](https://github.com/arien-ai/arien-ai-cli/discussions)

## 🙏 Acknowledgments

- [Deepseek](https://deepseek.com) for providing powerful AI models
- [Ollama](https://ollama.ai) for local AI model support
- [Blessed](https://github.com/chjj/blessed) for terminal UI components
- [Commander.js](https://github.com/tj/commander.js) for CLI framework

---

**Made with ❤️ by the Arien AI team**
