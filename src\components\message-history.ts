import { EventEmitter } from 'events';
import { LLMMessage, TerminalSession } from '@/types';
import { format, isToday, isYesterday, startOfDay, endOfDay } from 'date-fns';
import chalk from 'chalk';

export interface MessageHistoryOptions {
  maxMessages?: number;
  groupByDate?: boolean;
  showMetadata?: boolean;
  autoSave?: boolean;
  searchEnabled?: boolean;
}

export interface MessageGroup {
  date: Date;
  label: string;
  messages: LLMMessage[];
  count: number;
}

export interface SearchResult {
  message: LLMMessage;
  sessionId: string;
  sessionName: string;
  matchedText: string;
  context: LLMMessage[];
}

export class MessageHistoryManager extends EventEmitter {
  private options: Required<MessageHistoryOptions>;
  private messageIndex: Map<string, LLMMessage> = new Map();
  private sessionMessages: Map<string, LLMMessage[]> = new Map();
  private searchIndex: Map<string, Set<string>> = new Map(); // word -> message IDs

  constructor(options: MessageHistoryOptions = {}) {
    super();
    
    this.options = {
      maxMessages: 10000,
      groupByDate: true,
      showMetadata: true,
      autoSave: true,
      searchEnabled: true,
      ...options
    };
  }

  /**
   * Add message to history
   */
  public addMessage(message: LLMMessage, sessionId: string): void {
    // Add to message index
    this.messageIndex.set(message.id, message);

    // Add to session messages
    if (!this.sessionMessages.has(sessionId)) {
      this.sessionMessages.set(sessionId, []);
    }
    
    const sessionMessages = this.sessionMessages.get(sessionId)!;
    sessionMessages.push(message);

    // Update search index
    if (this.options.searchEnabled) {
      this.updateSearchIndex(message);
    }

    // Enforce message limit
    this.enforceMessageLimit();

    this.emit('message-added', { message, sessionId });
  }

  /**
   * Get messages for a session
   */
  public getSessionMessages(sessionId: string): LLMMessage[] {
    return this.sessionMessages.get(sessionId) || [];
  }

  /**
   * Get all messages across all sessions
   */
  public getAllMessages(): LLMMessage[] {
    const allMessages: LLMMessage[] = [];
    
    for (const messages of this.sessionMessages.values()) {
      allMessages.push(...messages);
    }

    return allMessages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }

  /**
   * Get messages grouped by date
   */
  public getMessagesByDate(sessionId?: string): MessageGroup[] {
    const messages = sessionId ? 
      this.getSessionMessages(sessionId) : 
      this.getAllMessages();

    if (!this.options.groupByDate) {
      return [{
        date: new Date(),
        label: 'All Messages',
        messages,
        count: messages.length
      }];
    }

    const groups = new Map<string, MessageGroup>();

    for (const message of messages) {
      const dateKey = format(startOfDay(message.timestamp), 'yyyy-MM-dd');
      
      if (!groups.has(dateKey)) {
        const label = this.formatDateLabel(message.timestamp);
        groups.set(dateKey, {
          date: message.timestamp,
          label,
          messages: [],
          count: 0
        });
      }

      const group = groups.get(dateKey)!;
      group.messages.push(message);
      group.count++;
    }

    return Array.from(groups.values()).sort((a, b) => 
      b.date.getTime() - a.date.getTime()
    );
  }

  /**
   * Format date label for grouping
   */
  private formatDateLabel(date: Date): string {
    if (isToday(date)) {
      return 'Today';
    } else if (isYesterday(date)) {
      return 'Yesterday';
    } else {
      return format(date, 'MMMM d, yyyy');
    }
  }

  /**
   * Search messages
   */
  public searchMessages(
    query: string, 
    options: {
      sessionId?: string;
      role?: string;
      dateRange?: { start: Date; end: Date };
      caseSensitive?: boolean;
      exactMatch?: boolean;
    } = {}
  ): SearchResult[] {
    if (!this.options.searchEnabled) {
      return [];
    }

    const results: SearchResult[] = [];
    const searchTerm = options.caseSensitive ? query : query.toLowerCase();
    
    // Get messages to search
    const messages = options.sessionId ? 
      this.getSessionMessages(options.sessionId) : 
      this.getAllMessages();

    for (const message of messages) {
      // Apply filters
      if (options.role && message.role !== options.role) {
        continue;
      }

      if (options.dateRange) {
        const messageDate = message.timestamp;
        if (messageDate < options.dateRange.start || messageDate > options.dateRange.end) {
          continue;
        }
      }

      // Search content
      const content = options.caseSensitive ? message.content : message.content.toLowerCase();
      const isMatch = options.exactMatch ? 
        content === searchTerm : 
        content.includes(searchTerm);

      if (isMatch) {
        // Find session info
        const sessionId = this.findMessageSession(message.id);
        const sessionName = sessionId ? `Session ${sessionId.slice(0, 8)}` : 'Unknown';

        // Get context messages (2 before, 2 after)
        const context = this.getMessageContext(message.id, sessionId, 2);

        // Extract matched text with context
        const matchedText = this.extractMatchedText(content, searchTerm, 50);

        results.push({
          message,
          sessionId: sessionId || '',
          sessionName,
          matchedText,
          context
        });
      }
    }

    return results.sort((a, b) => 
      b.message.timestamp.getTime() - a.message.timestamp.getTime()
    );
  }

  /**
   * Find which session a message belongs to
   */
  private findMessageSession(messageId: string): string | null {
    for (const [sessionId, messages] of this.sessionMessages.entries()) {
      if (messages.some(msg => msg.id === messageId)) {
        return sessionId;
      }
    }
    return null;
  }

  /**
   * Get context messages around a specific message
   */
  private getMessageContext(messageId: string, sessionId: string | null, contextSize: number): LLMMessage[] {
    if (!sessionId) return [];

    const sessionMessages = this.getSessionMessages(sessionId);
    const messageIndex = sessionMessages.findIndex(msg => msg.id === messageId);
    
    if (messageIndex === -1) return [];

    const start = Math.max(0, messageIndex - contextSize);
    const end = Math.min(sessionMessages.length, messageIndex + contextSize + 1);

    return sessionMessages.slice(start, end);
  }

  /**
   * Extract matched text with surrounding context
   */
  private extractMatchedText(content: string, searchTerm: string, contextLength: number): string {
    const index = content.toLowerCase().indexOf(searchTerm.toLowerCase());
    if (index === -1) return content.slice(0, contextLength);

    const start = Math.max(0, index - contextLength / 2);
    const end = Math.min(content.length, index + searchTerm.length + contextLength / 2);

    let excerpt = content.slice(start, end);
    
    if (start > 0) excerpt = '...' + excerpt;
    if (end < content.length) excerpt = excerpt + '...';

    return excerpt;
  }

  /**
   * Update search index for a message
   */
  private updateSearchIndex(message: LLMMessage): void {
    const words = message.content.toLowerCase()
      .split(/\W+/)
      .filter(word => word.length > 2);

    for (const word of words) {
      if (!this.searchIndex.has(word)) {
        this.searchIndex.set(word, new Set());
      }
      this.searchIndex.get(word)!.add(message.id);
    }
  }

  /**
   * Get message statistics
   */
  public getStatistics(): {
    totalMessages: number;
    messagesByRole: Record<string, number>;
    messagesBySession: Record<string, number>;
    dateRange: { start: Date; end: Date } | null;
    averageMessageLength: number;
    mostActiveDay: string | null;
  } {
    const allMessages = this.getAllMessages();
    
    if (allMessages.length === 0) {
      return {
        totalMessages: 0,
        messagesByRole: {},
        messagesBySession: {},
        dateRange: null,
        averageMessageLength: 0,
        mostActiveDay: null
      };
    }

    // Count by role
    const messagesByRole: Record<string, number> = {};
    let totalLength = 0;

    for (const message of allMessages) {
      messagesByRole[message.role] = (messagesByRole[message.role] || 0) + 1;
      totalLength += message.content.length;
    }

    // Count by session
    const messagesBySession: Record<string, number> = {};
    for (const [sessionId, messages] of this.sessionMessages.entries()) {
      messagesBySession[sessionId] = messages.length;
    }

    // Date range
    const timestamps = allMessages.map(msg => msg.timestamp.getTime());
    const dateRange = {
      start: new Date(Math.min(...timestamps)),
      end: new Date(Math.max(...timestamps))
    };

    // Most active day
    const dayGroups = this.getMessagesByDate();
    const mostActiveDay = dayGroups.length > 0 ? 
      dayGroups.reduce((max, group) => group.count > max.count ? group : max).label : 
      null;

    return {
      totalMessages: allMessages.length,
      messagesByRole,
      messagesBySession,
      dateRange,
      averageMessageLength: Math.round(totalLength / allMessages.length),
      mostActiveDay
    };
  }

  /**
   * Export message history
   */
  public exportHistory(exportFormat: 'json' | 'text' = 'json'): string {
    const allMessages = this.getAllMessages();

    if (exportFormat === 'text') {
      return allMessages.map(msg => {
        const timestamp = format(msg.timestamp, 'yyyy-MM-dd HH:mm:ss');
        return `[${timestamp}] ${msg.role.toUpperCase()}: ${msg.content}`;
      }).join('\n\n');
    }

    return JSON.stringify({
      exportDate: new Date().toISOString(),
      totalMessages: allMessages.length,
      sessions: Object.fromEntries(this.sessionMessages.entries()),
      statistics: this.getStatistics()
    }, null, 2);
  }

  /**
   * Import message history
   */
  public importHistory(data: string): void {
    try {
      const parsed = JSON.parse(data);
      
      if (parsed.sessions) {
        this.sessionMessages.clear();
        this.messageIndex.clear();
        this.searchIndex.clear();

        for (const [sessionId, messages] of Object.entries(parsed.sessions)) {
          const sessionMessages = (messages as any[]).map(msg => ({
            ...msg,
            timestamp: new Date(msg.timestamp)
          }));

          this.sessionMessages.set(sessionId, sessionMessages);
          
          for (const message of sessionMessages) {
            this.messageIndex.set(message.id, message);
            if (this.options.searchEnabled) {
              this.updateSearchIndex(message);
            }
          }
        }

        this.emit('history-imported', this.getAllMessages().length);
      }
    } catch (error) {
      this.emit('import-error', error);
    }
  }

  /**
   * Clear message history
   */
  public clearHistory(sessionId?: string): void {
    if (sessionId) {
      // Clear specific session
      const messages = this.sessionMessages.get(sessionId) || [];
      for (const message of messages) {
        this.messageIndex.delete(message.id);
      }
      this.sessionMessages.delete(sessionId);
      this.emit('session-cleared', sessionId);
    } else {
      // Clear all history
      this.sessionMessages.clear();
      this.messageIndex.clear();
      this.searchIndex.clear();
      this.emit('history-cleared');
    }
  }

  /**
   * Enforce message limit
   */
  private enforceMessageLimit(): void {
    const totalMessages = this.getAllMessages().length;
    
    if (totalMessages > this.options.maxMessages) {
      // Remove oldest messages
      const excess = totalMessages - this.options.maxMessages;
      const allMessages = this.getAllMessages();
      const toRemove = allMessages.slice(0, excess);

      for (const message of toRemove) {
        this.messageIndex.delete(message.id);
        
        // Remove from session
        const sessionId = this.findMessageSession(message.id);
        if (sessionId) {
          const sessionMessages = this.sessionMessages.get(sessionId)!;
          const index = sessionMessages.findIndex(msg => msg.id === message.id);
          if (index !== -1) {
            sessionMessages.splice(index, 1);
          }
        }
      }

      this.emit('messages-pruned', excess);
    }
  }

  /**
   * Get recent messages
   */
  public getRecentMessages(count: number = 50, sessionId?: string): LLMMessage[] {
    const messages = sessionId ? 
      this.getSessionMessages(sessionId) : 
      this.getAllMessages();

    return messages.slice(-count);
  }

  /**
   * Format message for display
   */
  public formatMessageForDisplay(message: LLMMessage, showMetadata: boolean = true): string {
    const timestamp = chalk.gray(format(message.timestamp, 'HH:mm:ss'));
    const role = this.formatRole(message.role);
    const content = message.content.length > 100 ? 
      message.content.slice(0, 100) + '...' : 
      message.content;

    let formatted = `${timestamp} ${role} ${content}`;

    if (showMetadata && this.options.showMetadata) {
      const metadata = [];
      if (message.toolCalls?.length) {
        metadata.push(chalk.magenta(`${message.toolCalls.length} tool calls`));
      }
      if (metadata.length > 0) {
        formatted += chalk.gray(` (${metadata.join(', ')})`);
      }
    }

    return formatted;
  }

  /**
   * Format role for display
   */
  private formatRole(role: string): string {
    const roleColors = {
      user: chalk.cyan('USER'),
      assistant: chalk.green('AI'),
      tool: chalk.magenta('TOOL'),
      system: chalk.yellow('SYS')
    };

    return roleColors[role as keyof typeof roleColors] || chalk.white(role.toUpperCase());
  }
}
