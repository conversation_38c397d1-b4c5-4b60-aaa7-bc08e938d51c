import { Config, ConfigSchema, ConfigError } from '../types/index.js';
import { z } from 'zod';

export class ConfigManager {
  private conf: any;
  private static instance: ConfigManager;

  private constructor() {
    // Check if we're in a test environment
    if (process.env.NODE_ENV === 'test' || process.env.JEST_WORKER_ID) {
      this.conf = this.createMockConf();
    } else {
      try {
        // Use require for better Jest compatibility
        const Conf = require('conf');
        this.conf = new Conf({
          projectName: 'arien-ai-cli',
          defaults: {
            provider: 'deepseek',
            model: 'deepseek-chat',
            maxTokens: 4096,
            temperature: 0.7,
            workingDirectory: process.cwd(),
            autoApprove: false,
            retryAttempts: 3,
            timeout: 30000
          }
        });
      } catch (error) {
        // Fallback for testing environment
        this.conf = this.createMockConf();
      }
    }
  }

  private createMockConf() {
    const store: any = {
      provider: 'deepseek',
      model: 'deepseek-chat',
      maxTokens: 4096,
      temperature: 0.7,
      workingDirectory: process.cwd(),
      autoApprove: false,
      retryAttempts: 3,
      timeout: 30000
    };

    return {
      store,
      get: (key: string) => store[key],
      set: (key: string, value: any) => { store[key] = value; },
      has: (key: string) => key in store,
      delete: (key: string) => { delete store[key]; },
      clear: () => { Object.keys(store).forEach(key => delete store[key]); },
      path: '/mock/config/path'
    };
  }

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  public get<K extends keyof Config>(key: K): Config[K] | undefined {
    return this.conf.get(key);
  }

  public set<K extends keyof Config>(key: K, value: Config[K]): void {
    // Basic validation for specific keys
    if (key === 'temperature' && (typeof value === 'number' && (value < 0 || value > 2))) {
      throw new ConfigError(`Invalid temperature: ${value}. Must be between 0 and 2.`);
    }
    if (key === 'provider' && value !== 'deepseek' && value !== 'ollama') {
      throw new ConfigError(`Invalid provider: ${value}. Must be 'deepseek' or 'ollama'.`);
    }

    this.conf.set(key, value);
  }

  public getAll(): Config {
    const config = this.conf.store;
    try {
      return ConfigSchema.parse(config);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new ConfigError(`Invalid configuration: ${error.message}`);
      }
      throw error;
    }
  }

  public getAllSafe(): Partial<Config> {
    // Get config without validation for internal use
    return this.conf.store as Partial<Config>;
  }

  public setAll(config: Partial<Config>): void {
    try {
      const currentConfig = this.getAllSafe();
      const newConfig = { ...currentConfig, ...config };
      const validatedConfig = ConfigSchema.parse(newConfig);

      Object.entries(validatedConfig).forEach(([key, value]) => {
        this.conf.set(key as keyof Config, value);
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new ConfigError(`Invalid configuration: ${error.message}`);
      }
      throw error;
    }
  }

  public reset(): void {
    this.conf.clear();
  }

  public delete<K extends keyof Config>(key: K): void {
    this.conf.delete(key);
  }

  public has<K extends keyof Config>(key: K): boolean {
    return this.conf.has(key);
  }

  public getConfigPath(): string {
    return this.conf.path;
  }

  public isConfigured(): boolean {
    try {
      const config = this.getAll();
      const provider = config.provider;

      if (provider === 'deepseek') {
        return !!(config.apiKey && config.model);
      } else if (provider === 'ollama') {
        return !!(config.baseUrl && config.model);
      }

      return false;
    } catch {
      return false;
    }
  }

  public validateConfig(): { valid: boolean; errors: string[] } {
    try {
      const config = this.getAll();
      ConfigSchema.parse(config);
      
      const errors: string[] = [];
      
      // Additional validation based on provider
      if (config.provider === 'deepseek' && !config.apiKey) {
        errors.push('API key is required for Deepseek provider');
      }
      
      if (config.provider === 'ollama' && !config.baseUrl) {
        errors.push('Base URL is required for Ollama provider');
      }
      
      return { valid: errors.length === 0, errors };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          valid: false,
          errors: error.errors.map(e => `${e.path.join('.')}: ${e.message}`)
        };
      }
      return { valid: false, errors: ['Unknown configuration error'] };
    }
  }

  public getProviderConfig() {
    try {
      const config = this.getAll();
      return {
        provider: config.provider,
        model: config.model,
        apiKey: config.apiKey,
        baseUrl: config.baseUrl,
        maxTokens: config.maxTokens,
        temperature: config.temperature,
        timeout: config.timeout
      };
    } catch {
      // Return safe partial config if full validation fails
      const config = this.getAllSafe();
      return {
        provider: config.provider,
        model: config.model,
        apiKey: config.apiKey,
        baseUrl: config.baseUrl,
        maxTokens: config.maxTokens,
        temperature: config.temperature,
        timeout: config.timeout
      };
    }
  }
}
